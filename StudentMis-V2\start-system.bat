@echo off
echo ==========================================
echo   Tsinghua StudentMIS V2.0 System Startup
echo   Complete System - No Nacos Required
echo ==========================================

echo.
echo Checking environment...

java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java environment not configured
    pause
    exit /b 1
)

mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Maven environment not configured
    pause
    exit /b 1
)

netstat -an | findstr :3306 >nul
if %errorlevel% neq 0 (
    echo [WARNING] MySQL service may not be running
)

echo [INFO] Environment check completed
echo.

echo Compiling entire project...
mvn clean install -DskipTests -q
if %errorlevel% neq 0 (
    echo [ERROR] Project compilation failed
    pause
    exit /b 1
)

echo [SUCCESS] Project compilation completed
echo.

echo Starting complete microservices system...
echo Note: This version has completely removed Nacos dependencies
echo.

echo [STARTING] API Gateway Service (Port 8080) - System Entry
start "Gateway-8080" cmd /k "title Gateway-8080 && cd studentmis-gateway && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo [STARTING] Authentication Service (Port 8081) - User Auth
start "Auth-8081" cmd /k "title Auth-8081 && cd studentmis-auth && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo [STARTING] Student Management Service (Port 8082) - Core Business
start "Student-8082" cmd /k "title Student-8082 && cd studentmis-student && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo [STARTING] Grade Management Service (Port 8083) - Grade Business
start "Grade-8083" cmd /k "title Grade-8083 && cd studentmis-grade && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

echo [STARTING] Analytics Service (Port 8084) - Data Analysis
start "Analytics-8084" cmd /k "title Analytics-8084 && cd studentmis-analytics && mvn spring-boot:run"

echo.
echo ==========================================
echo   Complete System Startup Completed
echo   Tsinghua University StudentMIS V2.0
echo ==========================================
echo.
echo System Architecture:
echo   - API Gateway:      http://localhost:8080 (System Entry)
echo   - Auth Service:     http://localhost:8081 (User Authentication)
echo   - Student Service:  http://localhost:8082 (Student Management)
echo   - Grade Service:    http://localhost:8083 (Grade Management)
echo   - Analytics Service: http://localhost:8084 (Data Analysis)
echo.
echo Core Function Tests:
echo   curl http://localhost:8082/api/students/simple/test
echo   curl http://localhost:8082/api/students/simple/by-student-id/2024140520
echo   curl http://localhost:8082/api/students/simple
echo.
echo Your Student Data:
echo   Student ID: 2024140520 (Xu Haoxiang)
echo   Major: Computer Science and Technology
echo   Class: Computer Class 4241
echo   Classmates: Wang Ziqi, Zhang Qingxiang, Cai Jiaxuan, Liu Chuandong, Shen Lei, Cheng Xuefeng
echo.
echo API Documentation:
echo   http://localhost:8080/swagger-ui.html (Gateway Unified Entry)
echo   http://localhost:8082/swagger-ui.html (Student Service Direct Access)
echo.
echo System Features:
echo   - No Nacos registry center required
echo   - Independent microservice architecture
echo   - Chinese interface and responses
echo   - Real student data
echo   - Enterprise-level architecture design
echo.
echo Please wait for all services to start completely (about 3-5 minutes)
echo When all windows show "Started Application", startup is successful
echo.

pause

2025/06/19-20:17:27.907221 5278 RocksDB version: 7.7.3
2025/06/19-20:17:27.907321 5278 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-20:17:27.907341 5278 Compile date 2022-10-24 17:17:55
2025/06/19-20:17:27.907354 5278 DB SUMMARY
2025/06/19-20:17:27.907369 5278 DB Session ID:  EI1DCCUQWNAYVIOM60QB
2025/06/19-20:17:27.908165 5278 CURRENT file:  CURRENT
2025/06/19-20:17:27.908185 5278 IDENTITY file:  IDENTITY
2025/06/19-20:17:27.908286 5278 MANIFEST file:  MANIFEST-000019 size: 524 Bytes
2025/06/19-20:17:27.908297 5278 SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log dir, Total Num: 4, files: 000010.sst 000011.sst 000016.sst 000017.sst 
2025/06/19-20:17:27.908305 5278 Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log: 000018.log size: 187 ; 
2025/06/19-20:17:27.908428 5278                         Options.error_if_exists: 0
2025/06/19-20:17:27.908434 5278                       Options.create_if_missing: 1
2025/06/19-20:17:27.908437 5278                         Options.paranoid_checks: 1
2025/06/19-20:17:27.908439 5278             Options.flush_verify_memtable_count: 1
2025/06/19-20:17:27.908442 5278                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-20:17:27.908444 5278        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-20:17:27.908446 5278                                     Options.env: 000001EA6A04C950
2025/06/19-20:17:27.908450 5278                                      Options.fs: WinFS
2025/06/19-20:17:27.908452 5278                                Options.info_log: 000001EA69BAEC00
2025/06/19-20:17:27.908455 5278                Options.max_file_opening_threads: 16
2025/06/19-20:17:27.908457 5278                              Options.statistics: 000001EA689E1A00
2025/06/19-20:17:27.908460 5278                               Options.use_fsync: 0
2025/06/19-20:17:27.908462 5278                       Options.max_log_file_size: 0
2025/06/19-20:17:27.908465 5278                  Options.max_manifest_file_size: 1073741824
2025/06/19-20:17:27.908467 5278                   Options.log_file_time_to_roll: 0
2025/06/19-20:17:27.908469 5278                       Options.keep_log_file_num: 100
2025/06/19-20:17:27.908472 5278                    Options.recycle_log_file_num: 0
2025/06/19-20:17:27.908474 5278                         Options.allow_fallocate: 1
2025/06/19-20:17:27.908477 5278                        Options.allow_mmap_reads: 0
2025/06/19-20:17:27.908479 5278                       Options.allow_mmap_writes: 0
2025/06/19-20:17:27.908481 5278                        Options.use_direct_reads: 0
2025/06/19-20:17:27.908484 5278                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-20:17:27.908486 5278          Options.create_missing_column_families: 1
2025/06/19-20:17:27.908489 5278                              Options.db_log_dir: 
2025/06/19-20:17:27.908491 5278                                 Options.wal_dir: 
2025/06/19-20:17:27.908493 5278                Options.table_cache_numshardbits: 6
2025/06/19-20:17:27.908496 5278                         Options.WAL_ttl_seconds: 0
2025/06/19-20:17:27.908498 5278                       Options.WAL_size_limit_MB: 0
2025/06/19-20:17:27.908500 5278                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-20:17:27.908503 5278             Options.manifest_preallocation_size: 4194304
2025/06/19-20:17:27.908505 5278                     Options.is_fd_close_on_exec: 1
2025/06/19-20:17:27.908507 5278                   Options.advise_random_on_open: 1
2025/06/19-20:17:27.908510 5278                    Options.db_write_buffer_size: 0
2025/06/19-20:17:27.908512 5278                    Options.write_buffer_manager: 000001EA6A04C3B0
2025/06/19-20:17:27.908515 5278         Options.access_hint_on_compaction_start: 1
2025/06/19-20:17:27.908517 5278           Options.random_access_max_buffer_size: 1048576
2025/06/19-20:17:27.908519 5278                      Options.use_adaptive_mutex: 0
2025/06/19-20:17:27.908553 5278                            Options.rate_limiter: 0000000000000000
2025/06/19-20:17:27.908559 5278     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-20:17:27.908562 5278                       Options.wal_recovery_mode: 2
2025/06/19-20:17:27.908564 5278                  Options.enable_thread_tracking: 0
2025/06/19-20:17:27.908567 5278                  Options.enable_pipelined_write: 0
2025/06/19-20:17:27.908569 5278                  Options.unordered_write: 0
2025/06/19-20:17:27.908571 5278         Options.allow_concurrent_memtable_write: 1
2025/06/19-20:17:27.908574 5278      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-20:17:27.908576 5278             Options.write_thread_max_yield_usec: 100
2025/06/19-20:17:27.908579 5278            Options.write_thread_slow_yield_usec: 3
2025/06/19-20:17:27.908581 5278                               Options.row_cache: None
2025/06/19-20:17:27.908584 5278                              Options.wal_filter: None
2025/06/19-20:17:27.908586 5278             Options.avoid_flush_during_recovery: 0
2025/06/19-20:17:27.908589 5278             Options.allow_ingest_behind: 0
2025/06/19-20:17:27.908591 5278             Options.two_write_queues: 0
2025/06/19-20:17:27.908593 5278             Options.manual_wal_flush: 0
2025/06/19-20:17:27.908596 5278             Options.wal_compression: 0
2025/06/19-20:17:27.908598 5278             Options.atomic_flush: 0
2025/06/19-20:17:27.908600 5278             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-20:17:27.908602 5278                 Options.persist_stats_to_disk: 0
2025/06/19-20:17:27.908605 5278                 Options.write_dbid_to_manifest: 0
2025/06/19-20:17:27.908607 5278                 Options.log_readahead_size: 0
2025/06/19-20:17:27.908610 5278                 Options.file_checksum_gen_factory: Unknown
2025/06/19-20:17:27.908613 5278                 Options.best_efforts_recovery: 0
2025/06/19-20:17:27.908615 5278                Options.max_bgerror_resume_count: 2147483647
2025/06/19-20:17:27.908617 5278            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-20:17:27.908620 5278             Options.allow_data_in_errors: 0
2025/06/19-20:17:27.908622 5278             Options.db_host_id: __hostname__
2025/06/19-20:17:27.908624 5278             Options.enforce_single_del_contracts: true
2025/06/19-20:17:27.908627 5278             Options.max_background_jobs: 2
2025/06/19-20:17:27.908629 5278             Options.max_background_compactions: 4
2025/06/19-20:17:27.908632 5278             Options.max_subcompactions: 1
2025/06/19-20:17:27.908634 5278             Options.avoid_flush_during_shutdown: 0
2025/06/19-20:17:27.908636 5278           Options.writable_file_max_buffer_size: 1048576
2025/06/19-20:17:27.908639 5278             Options.delayed_write_rate : 16777216
2025/06/19-20:17:27.908641 5278             Options.max_total_wal_size: 1073741824
2025/06/19-20:17:27.908644 5278             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-20:17:27.908646 5278                   Options.stats_dump_period_sec: 600
2025/06/19-20:17:27.908649 5278                 Options.stats_persist_period_sec: 600
2025/06/19-20:17:27.908651 5278                 Options.stats_history_buffer_size: 1048576
2025/06/19-20:17:27.908653 5278                          Options.max_open_files: -1
2025/06/19-20:17:27.908656 5278                          Options.bytes_per_sync: 0
2025/06/19-20:17:27.908658 5278                      Options.wal_bytes_per_sync: 0
2025/06/19-20:17:27.908661 5278                   Options.strict_bytes_per_sync: 0
2025/06/19-20:17:27.908663 5278       Options.compaction_readahead_size: 0
2025/06/19-20:17:27.908665 5278                  Options.max_background_flushes: 1
2025/06/19-20:17:27.908668 5278 Compression algorithms supported:
2025/06/19-20:17:27.908671 5278 	kZSTD supported: 1
2025/06/19-20:17:27.908674 5278 	kSnappyCompression supported: 1
2025/06/19-20:17:27.908677 5278 	kBZip2Compression supported: 0
2025/06/19-20:17:27.908692 5278 	kZlibCompression supported: 1
2025/06/19-20:17:27.908695 5278 	kLZ4Compression supported: 1
2025/06/19-20:17:27.908697 5278 	kXpressCompression supported: 0
2025/06/19-20:17:27.908699 5278 	kLZ4HCCompression supported: 1
2025/06/19-20:17:27.908702 5278 	kZSTDNotFinalCompression supported: 1
2025/06/19-20:17:27.908705 5278 Fast CRC32 supported: Not supported on x86
2025/06/19-20:17:27.908707 5278 DMutex implementation: std::mutex
2025/06/19-20:17:27.909752 5278 [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000019
2025/06/19-20:17:27.909919 5278 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-20:17:27.909924 5278               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:17:27.909927 5278           Options.merge_operator: StringAppendOperator
2025/06/19-20:17:27.909930 5278        Options.compaction_filter: None
2025/06/19-20:17:27.909932 5278        Options.compaction_filter_factory: None
2025/06/19-20:17:27.909935 5278  Options.sst_partitioner_factory: None
2025/06/19-20:17:27.909937 5278         Options.memtable_factory: SkipListFactory
2025/06/19-20:17:27.909939 5278            Options.table_factory: BlockBasedTable
2025/06/19-20:17:27.909963 5278            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001EA68E2B7D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000001EA69D76790
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:17:27.909967 5278        Options.write_buffer_size: 67108864
2025/06/19-20:17:27.909969 5278  Options.max_write_buffer_number: 3
2025/06/19-20:17:27.909972 5278          Options.compression: Snappy
2025/06/19-20:17:27.909974 5278                  Options.bottommost_compression: Disabled
2025/06/19-20:17:27.909977 5278       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:17:27.909979 5278   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:17:27.909982 5278             Options.num_levels: 7
2025/06/19-20:17:27.909984 5278        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:17:27.909986 5278     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:17:27.909989 5278     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:17:27.909991 5278            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:17:27.909994 5278                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:17:27.909996 5278               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:17:27.909999 5278         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.910001 5278         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.910003 5278         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:17:27.910006 5278                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:17:27.910011 5278         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.910015 5278         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.910018 5278            Options.compression_opts.window_bits: -14
2025/06/19-20:17:27.910020 5278                  Options.compression_opts.level: 32767
2025/06/19-20:17:27.910022 5278               Options.compression_opts.strategy: 0
2025/06/19-20:17:27.910025 5278         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.910027 5278         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.910029 5278         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.910032 5278         Options.compression_opts.parallel_threads: 1
2025/06/19-20:17:27.910034 5278                  Options.compression_opts.enabled: false
2025/06/19-20:17:27.910037 5278         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.910039 5278      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:17:27.910042 5278          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:17:27.910044 5278              Options.level0_stop_writes_trigger: 40
2025/06/19-20:17:27.910046 5278                   Options.target_file_size_base: 67108864
2025/06/19-20:17:27.910049 5278             Options.target_file_size_multiplier: 1
2025/06/19-20:17:27.910051 5278                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:17:27.910054 5278 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:17:27.910056 5278          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:17:27.910059 5278 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:17:27.910062 5278 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:17:27.910064 5278 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:17:27.910066 5278 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:17:27.910069 5278 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:17:27.910071 5278 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:17:27.910074 5278 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:17:27.910076 5278       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:17:27.910079 5278                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:17:27.910081 5278                        Options.arena_block_size: 1048576
2025/06/19-20:17:27.910084 5278   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:17:27.910086 5278   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:17:27.910089 5278                Options.disable_auto_compactions: 0
2025/06/19-20:17:27.910092 5278                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:17:27.910095 5278                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:17:27.910097 5278 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:17:27.910100 5278 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:17:27.910102 5278 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:17:27.910105 5278 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:17:27.910107 5278 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:17:27.910110 5278 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:17:27.910113 5278 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:17:27.910115 5278 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:17:27.910119 5278                   Options.table_properties_collectors: 
2025/06/19-20:17:27.910121 5278                   Options.inplace_update_support: 0
2025/06/19-20:17:27.910124 5278                 Options.inplace_update_num_locks: 10000
2025/06/19-20:17:27.910126 5278               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:17:27.910154 5278               Options.memtable_whole_key_filtering: 0
2025/06/19-20:17:27.910157 5278   Options.memtable_huge_page_size: 0
2025/06/19-20:17:27.910159 5278                           Options.bloom_locality: 0
2025/06/19-20:17:27.910162 5278                    Options.max_successive_merges: 0
2025/06/19-20:17:27.910164 5278                Options.optimize_filters_for_hits: 0
2025/06/19-20:17:27.910166 5278                Options.paranoid_file_checks: 0
2025/06/19-20:17:27.910169 5278                Options.force_consistency_checks: 1
2025/06/19-20:17:27.910171 5278                Options.report_bg_io_stats: 0
2025/06/19-20:17:27.910173 5278                               Options.ttl: 2592000
2025/06/19-20:17:27.910176 5278          Options.periodic_compaction_seconds: 0
2025/06/19-20:17:27.910178 5278  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:17:27.910180 5278                       Options.enable_blob_files: false
2025/06/19-20:17:27.910183 5278                           Options.min_blob_size: 0
2025/06/19-20:17:27.910185 5278                          Options.blob_file_size: 268435456
2025/06/19-20:17:27.910188 5278                   Options.blob_compression_type: NoCompression
2025/06/19-20:17:27.910190 5278          Options.enable_blob_garbage_collection: false
2025/06/19-20:17:27.910193 5278      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:17:27.910195 5278 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:17:27.910198 5278          Options.blob_compaction_readahead_size: 0
2025/06/19-20:17:27.910201 5278                Options.blob_file_starting_level: 0
2025/06/19-20:17:27.910203 5278 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:17:27.911669 5278 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-20:17:27.911681 5278               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:17:27.911684 5278           Options.merge_operator: StringAppendOperator
2025/06/19-20:17:27.911687 5278        Options.compaction_filter: None
2025/06/19-20:17:27.911689 5278        Options.compaction_filter_factory: None
2025/06/19-20:17:27.911691 5278  Options.sst_partitioner_factory: None
2025/06/19-20:17:27.911694 5278         Options.memtable_factory: SkipListFactory
2025/06/19-20:17:27.911697 5278            Options.table_factory: BlockBasedTable
2025/06/19-20:17:27.911727 5278            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001EA68E2B7D0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000001EA69D76790
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:17:27.911731 5278        Options.write_buffer_size: 67108864
2025/06/19-20:17:27.911733 5278  Options.max_write_buffer_number: 3
2025/06/19-20:17:27.911736 5278          Options.compression: Snappy
2025/06/19-20:17:27.911738 5278                  Options.bottommost_compression: Disabled
2025/06/19-20:17:27.911741 5278       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:17:27.911781 5278   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:17:27.911785 5278             Options.num_levels: 7
2025/06/19-20:17:27.911787 5278        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:17:27.911790 5278     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:17:27.911792 5278     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:17:27.911795 5278            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:17:27.911798 5278                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:17:27.911800 5278               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:17:27.911805 5278         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.911807 5278         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.911810 5278         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:17:27.911813 5278                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:17:27.911815 5278         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.911818 5278         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.911820 5278            Options.compression_opts.window_bits: -14
2025/06/19-20:17:27.911823 5278                  Options.compression_opts.level: 32767
2025/06/19-20:17:27.911825 5278               Options.compression_opts.strategy: 0
2025/06/19-20:17:27.911828 5278         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.911831 5278         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.911833 5278         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.911836 5278         Options.compression_opts.parallel_threads: 1
2025/06/19-20:17:27.911838 5278                  Options.compression_opts.enabled: false
2025/06/19-20:17:27.911841 5278         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.911843 5278      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:17:27.911846 5278          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:17:27.911848 5278              Options.level0_stop_writes_trigger: 40
2025/06/19-20:17:27.911851 5278                   Options.target_file_size_base: 67108864
2025/06/19-20:17:27.911853 5278             Options.target_file_size_multiplier: 1
2025/06/19-20:17:27.911856 5278                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:17:27.911859 5278 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:17:27.911861 5278          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:17:27.911865 5278 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:17:27.911867 5278 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:17:27.911870 5278 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:17:27.911872 5278 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:17:27.911875 5278 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:17:27.911878 5278 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:17:27.911880 5278 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:17:27.911883 5278       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:17:27.911885 5278                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:17:27.911888 5278                        Options.arena_block_size: 1048576
2025/06/19-20:17:27.911890 5278   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:17:27.911893 5278   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:17:27.911896 5278                Options.disable_auto_compactions: 0
2025/06/19-20:17:27.911899 5278                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:17:27.911902 5278                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:17:27.911923 5278 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:17:27.911926 5278 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:17:27.911928 5278 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:17:27.911931 5278 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:17:27.911934 5278 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:17:27.911937 5278 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:17:27.911940 5278 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:17:27.911942 5278 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:17:27.911947 5278                   Options.table_properties_collectors: 
2025/06/19-20:17:27.911950 5278                   Options.inplace_update_support: 0
2025/06/19-20:17:27.911952 5278                 Options.inplace_update_num_locks: 10000
2025/06/19-20:17:27.911955 5278               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:17:27.911958 5278               Options.memtable_whole_key_filtering: 0
2025/06/19-20:17:27.911960 5278   Options.memtable_huge_page_size: 0
2025/06/19-20:17:27.911963 5278                           Options.bloom_locality: 0
2025/06/19-20:17:27.911965 5278                    Options.max_successive_merges: 0
2025/06/19-20:17:27.911968 5278                Options.optimize_filters_for_hits: 0
2025/06/19-20:17:27.911970 5278                Options.paranoid_file_checks: 0
2025/06/19-20:17:27.911973 5278                Options.force_consistency_checks: 1
2025/06/19-20:17:27.911975 5278                Options.report_bg_io_stats: 0
2025/06/19-20:17:27.911978 5278                               Options.ttl: 2592000
2025/06/19-20:17:27.911980 5278          Options.periodic_compaction_seconds: 0
2025/06/19-20:17:27.911983 5278  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:17:27.911985 5278                       Options.enable_blob_files: false
2025/06/19-20:17:27.911988 5278                           Options.min_blob_size: 0
2025/06/19-20:17:27.911990 5278                          Options.blob_file_size: 268435456
2025/06/19-20:17:27.911993 5278                   Options.blob_compression_type: NoCompression
2025/06/19-20:17:27.911995 5278          Options.enable_blob_garbage_collection: false
2025/06/19-20:17:27.911998 5278      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:17:27.912001 5278 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:17:27.912004 5278          Options.blob_compaction_readahead_size: 0
2025/06/19-20:17:27.912006 5278                Options.blob_file_starting_level: 0
2025/06/19-20:17:27.912009 5278 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:17:27.915522 5278 [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000019 succeeded,manifest_file_number is 19, next_file_number is 21, last_sequence is 5, log_number is 13,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 13
2025/06/19-20:17:27.915536 5278 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 13
2025/06/19-20:17:27.915540 5278 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 13
2025/06/19-20:17:27.915924 5278 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9e-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-20:17:27.917609 5278 EVENT_LOG_v1 {"time_micros": 1750335447917601, "job": 1, "event": "recovery_started", "wal_files": [18]}
2025/06/19-20:17:27.917618 5278 [db\db_impl\db_impl_open.cc:1029] Recovering log #18 mode 2
2025/06/19-20:17:27.919230 5278 EVENT_LOG_v1 {"time_micros": 1750335447919196, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 22, "file_size": 1184, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 6, "largest_seqno": 8, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335447, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9e-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "EI1DCCUQWNAYVIOM60QB", "orig_file_number": 22, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:17:27.923113 5278 EVENT_LOG_v1 {"time_micros": 1750335447923074, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 23, "file_size": 1190, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 7, "largest_seqno": 9, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335447, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9e-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "EI1DCCUQWNAYVIOM60QB", "orig_file_number": 23, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:17:27.926000 5278 EVENT_LOG_v1 {"time_micros": 1750335447925992, "job": 1, "event": "recovery_finished"}
2025/06/19-20:17:27.926414 5278 [db\version_set.cc:5051] Creating manifest 25
2025/06/19-20:17:27.934435 5278 [file\delete_scheduler.cc:77] Deleted file D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/000018.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-20:17:27.934470 5278 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 000001EA6A4E8040
2025/06/19-20:17:27.935589 5278 DB pointer 000001EA6692F480
2025/06/19-20:17:30.942706 1ce0 [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-20:17:30.942735 1ce0 [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 3 writes, 4 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 4 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.34 KB   0.3      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      3/0    3.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000001EA69D76790#8752 capacity: 512.00 MB usage: 2.85 KB table_size: 4096 occupancy: 15 collections: 1 last_copies: 1 last_secs: 0.000144 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(6,0.82 KB,0.000155531%) IndexBlock(6,0.55 KB,0.000104681%) OtherBlock(2,0.21 KB,3.98606e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.41 KB   0.3      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      3/0    3.41 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000001EA69D76790#8752 capacity: 512.00 MB usage: 2.85 KB table_size: 4096 occupancy: 15 collections: 1 last_copies: 1 last_secs: 0.000144 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(6,0.82 KB,0.000155531%) IndexBlock(6,0.55 KB,0.000104681%) OtherBlock(2,0.21 KB,3.98606e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 9 Average: 14.8889  StdDev: 9.07
Min: 6  Median: 11.2500  Max: 32
Percentiles: P50: 11.25 P75: 20.25 P99: 32.00 P99.9: 32.00 P99.99: 32.00
------------------------------------------------------
(       4,       6 ]        1  11.111%  11.111% ##
(       6,      10 ]        3  33.333%  44.444% #######
(      10,      15 ]        2  22.222%  66.667% ####
(      15,      22 ]        1  11.111%  77.778% ##
(      22,      34 ]        2  22.222% 100.000% ####


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 9 Average: 12.4444  StdDev: 5.44
Min: 7  Median: 9.6000  Max: 21
Percentiles: P50: 9.60 P75: 16.75 P99: 21.00 P99.9: 21.00 P99.99: 21.00
------------------------------------------------------
(       6,      10 ]        5  55.556%  55.556% ###########
(      10,      15 ]        1  11.111%  66.667% ##
(      15,      22 ]        3  33.333% 100.000% #######

2025/06/19-20:17:30.943359 1ce0 [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 14
rocksdb.block.cache.hit COUNT : 8
rocksdb.block.cache.add COUNT : 14
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 6
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 6
rocksdb.block.cache.index.bytes.insert COUNT : 562
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 8
rocksdb.block.cache.data.hit COUNT : 8
rocksdb.block.cache.data.add COUNT : 8
rocksdb.block.cache.data.bytes.insert COUNT : 1049
rocksdb.block.cache.bytes.read COUNT : 1064
rocksdb.block.cache.bytes.write COUNT : 1611
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 5
rocksdb.l0.hit COUNT : 5
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 4
rocksdb.number.keys.read COUNT : 5
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 166
rocksdb.bytes.read COUNT : 170
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 4
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 2
rocksdb.number.db.next.found COUNT : 3
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 194
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 166
rocksdb.write.self COUNT : 3
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 3
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2374
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 3
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 14257
rocksdb.non.last.level.read.count COUNT : 18
rocksdb.block.checksum.compute.count COUNT : 26
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 8.000000 P95 : 66.000000 P99 : 66.000000 P100 : 66.000000 COUNT : 5 SUM : 128
rocksdb.db.write.micros P50 : 210.000000 P95 : 482.000000 P99 : 482.000000 P100 : 482.000000 COUNT : 3 SUM : 710
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 477.000000 P95 : 477.000000 P99 : 477.000000 P100 : 477.000000 COUNT : 2 SUM : 938
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 420.000000 P95 : 420.000000 P99 : 420.000000 P100 : 420.000000 COUNT : 1 SUM : 420
rocksdb.manifest.file.sync.micros P50 : 363.000000 P95 : 363.000000 P99 : 363.000000 P100 : 363.000000 COUNT : 1 SUM : 363
rocksdb.table.open.io.micros P50 : 101.500000 P95 : 156.000000 P99 : 156.000000 P100 : 156.000000 COUNT : 6 SUM : 657
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.833333 P95 : 22.000000 P99 : 27.000000 P100 : 27.000000 COUNT : 20 SUM : 97
rocksdb.write.raw.block.micros P50 : 0.750000 P95 : 3.400000 P99 : 3.880000 P100 : 4.000000 COUNT : 12 SUM : 13
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 10.000000 P95 : 28.600000 P99 : 32.000000 P100 : 32.000000 COUNT : 18 SUM : 246
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 34.000000 P95 : 34.000000 P99 : 34.000000 P100 : 34.000000 COUNT : 5 SUM : 170
rocksdb.bytes.per.write P50 : 31.000000 P95 : 103.000000 P99 : 103.000000 P100 : 103.000000 COUNT : 3 SUM : 166
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1085.000000 P95 : 1099.000000 P99 : 1099.000000 P100 : 1099.000000 COUNT : 6 SUM : 6319
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-20:17:31.941262 1ce0 [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-20:17:31.941289 1ce0 [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-20:17:31.941293 1ce0 [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0

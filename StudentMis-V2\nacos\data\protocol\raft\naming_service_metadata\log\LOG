2025/06/19-20:27:06.823838 34b0 RocksDB version: 7.7.3
2025/06/19-20:27:06.823905 34b0 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-20:27:06.823916 34b0 Compile date 2022-10-24 17:17:55
2025/06/19-20:27:06.823924 34b0 DB SUMMARY
2025/06/19-20:27:06.823931 34b0 DB Session ID:  Z9GJHPZR9V5DOT5YCAS0
2025/06/19-20:27:06.824602 34b0 CURRENT file:  CURRENT
2025/06/19-20:27:06.824615 34b0 IDENTITY file:  IDENTITY
2025/06/19-20:27:06.824717 34b0 MANIFEST file:  MANIFEST-000025 size: 686 Bytes
2025/06/19-20:27:06.824728 34b0 SST files in d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log dir, Total Num: 6, files: 000010.sst 000011.sst 000016.sst 000017.sst 000022.sst 000023.sst 
2025/06/19-20:27:06.824844 34b0 Write Ahead Log file in d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log: 000024.log size: 187 ; 
2025/06/19-20:27:06.824850 34b0                         Options.error_if_exists: 0
2025/06/19-20:27:06.824852 34b0                       Options.create_if_missing: 1
2025/06/19-20:27:06.824855 34b0                         Options.paranoid_checks: 1
2025/06/19-20:27:06.824857 34b0             Options.flush_verify_memtable_count: 1
2025/06/19-20:27:06.824860 34b0                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-20:27:06.824862 34b0        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-20:27:06.824864 34b0                                     Options.env: 0000018DC40F71D0
2025/06/19-20:27:06.824867 34b0                                      Options.fs: WinFS
2025/06/19-20:27:06.824869 34b0                                Options.info_log: 0000018DC4CE2530
2025/06/19-20:27:06.824872 34b0                Options.max_file_opening_threads: 16
2025/06/19-20:27:06.824874 34b0                              Options.statistics: 0000018DFD13B290
2025/06/19-20:27:06.824876 34b0                               Options.use_fsync: 0
2025/06/19-20:27:06.824879 34b0                       Options.max_log_file_size: 0
2025/06/19-20:27:06.824881 34b0                  Options.max_manifest_file_size: 1073741824
2025/06/19-20:27:06.824883 34b0                   Options.log_file_time_to_roll: 0
2025/06/19-20:27:06.824886 34b0                       Options.keep_log_file_num: 100
2025/06/19-20:27:06.824888 34b0                    Options.recycle_log_file_num: 0
2025/06/19-20:27:06.824890 34b0                         Options.allow_fallocate: 1
2025/06/19-20:27:06.824893 34b0                        Options.allow_mmap_reads: 0
2025/06/19-20:27:06.824895 34b0                       Options.allow_mmap_writes: 0
2025/06/19-20:27:06.824897 34b0                        Options.use_direct_reads: 0
2025/06/19-20:27:06.824899 34b0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-20:27:06.824902 34b0          Options.create_missing_column_families: 1
2025/06/19-20:27:06.824904 34b0                              Options.db_log_dir: 
2025/06/19-20:27:06.824906 34b0                                 Options.wal_dir: 
2025/06/19-20:27:06.824909 34b0                Options.table_cache_numshardbits: 6
2025/06/19-20:27:06.824911 34b0                         Options.WAL_ttl_seconds: 0
2025/06/19-20:27:06.824914 34b0                       Options.WAL_size_limit_MB: 0
2025/06/19-20:27:06.824916 34b0                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-20:27:06.824918 34b0             Options.manifest_preallocation_size: 4194304
2025/06/19-20:27:06.824921 34b0                     Options.is_fd_close_on_exec: 1
2025/06/19-20:27:06.824923 34b0                   Options.advise_random_on_open: 1
2025/06/19-20:27:06.824926 34b0                    Options.db_write_buffer_size: 0
2025/06/19-20:27:06.824928 34b0                    Options.write_buffer_manager: 0000018DC40F7C20
2025/06/19-20:27:06.824943 34b0         Options.access_hint_on_compaction_start: 1
2025/06/19-20:27:06.824946 34b0           Options.random_access_max_buffer_size: 1048576
2025/06/19-20:27:06.824971 34b0                      Options.use_adaptive_mutex: 0
2025/06/19-20:27:06.824974 34b0                            Options.rate_limiter: 0000000000000000
2025/06/19-20:27:06.824978 34b0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-20:27:06.824980 34b0                       Options.wal_recovery_mode: 2
2025/06/19-20:27:06.824983 34b0                  Options.enable_thread_tracking: 0
2025/06/19-20:27:06.824985 34b0                  Options.enable_pipelined_write: 0
2025/06/19-20:27:06.824987 34b0                  Options.unordered_write: 0
2025/06/19-20:27:06.824989 34b0         Options.allow_concurrent_memtable_write: 1
2025/06/19-20:27:06.824991 34b0      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-20:27:06.824994 34b0             Options.write_thread_max_yield_usec: 100
2025/06/19-20:27:06.824996 34b0            Options.write_thread_slow_yield_usec: 3
2025/06/19-20:27:06.824999 34b0                               Options.row_cache: None
2025/06/19-20:27:06.825001 34b0                              Options.wal_filter: None
2025/06/19-20:27:06.825003 34b0             Options.avoid_flush_during_recovery: 0
2025/06/19-20:27:06.825006 34b0             Options.allow_ingest_behind: 0
2025/06/19-20:27:06.825008 34b0             Options.two_write_queues: 0
2025/06/19-20:27:06.825010 34b0             Options.manual_wal_flush: 0
2025/06/19-20:27:06.825012 34b0             Options.wal_compression: 0
2025/06/19-20:27:06.825014 34b0             Options.atomic_flush: 0
2025/06/19-20:27:06.825016 34b0             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-20:27:06.825019 34b0                 Options.persist_stats_to_disk: 0
2025/06/19-20:27:06.825021 34b0                 Options.write_dbid_to_manifest: 0
2025/06/19-20:27:06.825023 34b0                 Options.log_readahead_size: 0
2025/06/19-20:27:06.825026 34b0                 Options.file_checksum_gen_factory: Unknown
2025/06/19-20:27:06.825028 34b0                 Options.best_efforts_recovery: 0
2025/06/19-20:27:06.825030 34b0                Options.max_bgerror_resume_count: 2147483647
2025/06/19-20:27:06.825033 34b0            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-20:27:06.825035 34b0             Options.allow_data_in_errors: 0
2025/06/19-20:27:06.825037 34b0             Options.db_host_id: __hostname__
2025/06/19-20:27:06.825039 34b0             Options.enforce_single_del_contracts: true
2025/06/19-20:27:06.825042 34b0             Options.max_background_jobs: 2
2025/06/19-20:27:06.825044 34b0             Options.max_background_compactions: 4
2025/06/19-20:27:06.825046 34b0             Options.max_subcompactions: 1
2025/06/19-20:27:06.825048 34b0             Options.avoid_flush_during_shutdown: 0
2025/06/19-20:27:06.825051 34b0           Options.writable_file_max_buffer_size: 1048576
2025/06/19-20:27:06.825053 34b0             Options.delayed_write_rate : 16777216
2025/06/19-20:27:06.825055 34b0             Options.max_total_wal_size: 1073741824
2025/06/19-20:27:06.825057 34b0             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-20:27:06.825060 34b0                   Options.stats_dump_period_sec: 600
2025/06/19-20:27:06.825062 34b0                 Options.stats_persist_period_sec: 600
2025/06/19-20:27:06.825064 34b0                 Options.stats_history_buffer_size: 1048576
2025/06/19-20:27:06.825067 34b0                          Options.max_open_files: -1
2025/06/19-20:27:06.825069 34b0                          Options.bytes_per_sync: 0
2025/06/19-20:27:06.825071 34b0                      Options.wal_bytes_per_sync: 0
2025/06/19-20:27:06.825074 34b0                   Options.strict_bytes_per_sync: 0
2025/06/19-20:27:06.825076 34b0       Options.compaction_readahead_size: 0
2025/06/19-20:27:06.825078 34b0                  Options.max_background_flushes: 1
2025/06/19-20:27:06.825080 34b0 Compression algorithms supported:
2025/06/19-20:27:06.825083 34b0 	kZSTD supported: 1
2025/06/19-20:27:06.825086 34b0 	kSnappyCompression supported: 1
2025/06/19-20:27:06.825088 34b0 	kBZip2Compression supported: 0
2025/06/19-20:27:06.825103 34b0 	kZlibCompression supported: 1
2025/06/19-20:27:06.825107 34b0 	kLZ4Compression supported: 1
2025/06/19-20:27:06.825109 34b0 	kXpressCompression supported: 0
2025/06/19-20:27:06.825111 34b0 	kLZ4HCCompression supported: 1
2025/06/19-20:27:06.825114 34b0 	kZSTDNotFinalCompression supported: 1
2025/06/19-20:27:06.825117 34b0 Fast CRC32 supported: Not supported on x86
2025/06/19-20:27:06.825120 34b0 DMutex implementation: std::mutex
2025/06/19-20:27:06.826062 34b0 [db\version_set.cc:5531] Recovering from manifest file: d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/MANIFEST-000025
2025/06/19-20:27:06.826227 34b0 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-20:27:06.826234 34b0               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:27:06.826236 34b0           Options.merge_operator: StringAppendOperator
2025/06/19-20:27:06.826239 34b0        Options.compaction_filter: None
2025/06/19-20:27:06.826241 34b0        Options.compaction_filter_factory: None
2025/06/19-20:27:06.826243 34b0  Options.sst_partitioner_factory: None
2025/06/19-20:27:06.826246 34b0         Options.memtable_factory: SkipListFactory
2025/06/19-20:27:06.826248 34b0            Options.table_factory: BlockBasedTable
2025/06/19-20:27:06.826270 34b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018DC4FE5B50)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018DFEB1CAA0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:27:06.826273 34b0        Options.write_buffer_size: 67108864
2025/06/19-20:27:06.826276 34b0  Options.max_write_buffer_number: 3
2025/06/19-20:27:06.826278 34b0          Options.compression: Snappy
2025/06/19-20:27:06.826280 34b0                  Options.bottommost_compression: Disabled
2025/06/19-20:27:06.826283 34b0       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:27:06.826285 34b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:27:06.826288 34b0             Options.num_levels: 7
2025/06/19-20:27:06.826290 34b0        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:27:06.826292 34b0     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:27:06.826295 34b0     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:27:06.826297 34b0            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:27:06.826299 34b0                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:27:06.826302 34b0               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:27:06.826304 34b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.826311 34b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.826313 34b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:27:06.826315 34b0                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:27:06.826321 34b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.826324 34b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.826327 34b0            Options.compression_opts.window_bits: -14
2025/06/19-20:27:06.826329 34b0                  Options.compression_opts.level: 32767
2025/06/19-20:27:06.826331 34b0               Options.compression_opts.strategy: 0
2025/06/19-20:27:06.826333 34b0         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.826336 34b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.826338 34b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.826340 34b0         Options.compression_opts.parallel_threads: 1
2025/06/19-20:27:06.826342 34b0                  Options.compression_opts.enabled: false
2025/06/19-20:27:06.826345 34b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.826347 34b0      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:27:06.826349 34b0          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:27:06.826351 34b0              Options.level0_stop_writes_trigger: 40
2025/06/19-20:27:06.826354 34b0                   Options.target_file_size_base: 67108864
2025/06/19-20:27:06.826356 34b0             Options.target_file_size_multiplier: 1
2025/06/19-20:27:06.826358 34b0                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:27:06.826360 34b0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:27:06.826363 34b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:27:06.826365 34b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:27:06.826368 34b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:27:06.826370 34b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:27:06.826372 34b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:27:06.826374 34b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:27:06.826377 34b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:27:06.826379 34b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:27:06.826381 34b0       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:27:06.826383 34b0                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:27:06.826386 34b0                        Options.arena_block_size: 1048576
2025/06/19-20:27:06.826388 34b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:27:06.826390 34b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:27:06.826393 34b0                Options.disable_auto_compactions: 0
2025/06/19-20:27:06.826396 34b0                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:27:06.826399 34b0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:27:06.826401 34b0 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:27:06.826403 34b0 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:27:06.826406 34b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:27:06.826408 34b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:27:06.826410 34b0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:27:06.826413 34b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:27:06.826415 34b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:27:06.826418 34b0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:27:06.826422 34b0                   Options.table_properties_collectors: 
2025/06/19-20:27:06.826424 34b0                   Options.inplace_update_support: 0
2025/06/19-20:27:06.826426 34b0                 Options.inplace_update_num_locks: 10000
2025/06/19-20:27:06.826428 34b0               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:27:06.826485 34b0               Options.memtable_whole_key_filtering: 0
2025/06/19-20:27:06.826490 34b0   Options.memtable_huge_page_size: 0
2025/06/19-20:27:06.826492 34b0                           Options.bloom_locality: 0
2025/06/19-20:27:06.826494 34b0                    Options.max_successive_merges: 0
2025/06/19-20:27:06.826496 34b0                Options.optimize_filters_for_hits: 0
2025/06/19-20:27:06.826499 34b0                Options.paranoid_file_checks: 0
2025/06/19-20:27:06.826501 34b0                Options.force_consistency_checks: 1
2025/06/19-20:27:06.826503 34b0                Options.report_bg_io_stats: 0
2025/06/19-20:27:06.826506 34b0                               Options.ttl: 2592000
2025/06/19-20:27:06.826508 34b0          Options.periodic_compaction_seconds: 0
2025/06/19-20:27:06.826510 34b0  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:27:06.826513 34b0                       Options.enable_blob_files: false
2025/06/19-20:27:06.826515 34b0                           Options.min_blob_size: 0
2025/06/19-20:27:06.826517 34b0                          Options.blob_file_size: 268435456
2025/06/19-20:27:06.826520 34b0                   Options.blob_compression_type: NoCompression
2025/06/19-20:27:06.826522 34b0          Options.enable_blob_garbage_collection: false
2025/06/19-20:27:06.826525 34b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:27:06.826527 34b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:27:06.826530 34b0          Options.blob_compaction_readahead_size: 0
2025/06/19-20:27:06.826532 34b0                Options.blob_file_starting_level: 0
2025/06/19-20:27:06.826534 34b0 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:27:06.827953 34b0 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-20:27:06.827963 34b0               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:27:06.827966 34b0           Options.merge_operator: StringAppendOperator
2025/06/19-20:27:06.827968 34b0        Options.compaction_filter: None
2025/06/19-20:27:06.827970 34b0        Options.compaction_filter_factory: None
2025/06/19-20:27:06.827973 34b0  Options.sst_partitioner_factory: None
2025/06/19-20:27:06.827975 34b0         Options.memtable_factory: SkipListFactory
2025/06/19-20:27:06.827977 34b0            Options.table_factory: BlockBasedTable
2025/06/19-20:27:06.828002 34b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018DC4FE5B50)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018DFEB1CAA0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:27:06.828006 34b0        Options.write_buffer_size: 67108864
2025/06/19-20:27:06.828008 34b0  Options.max_write_buffer_number: 3
2025/06/19-20:27:06.828010 34b0          Options.compression: Snappy
2025/06/19-20:27:06.828013 34b0                  Options.bottommost_compression: Disabled
2025/06/19-20:27:06.828015 34b0       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:27:06.828056 34b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:27:06.828059 34b0             Options.num_levels: 7
2025/06/19-20:27:06.828061 34b0        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:27:06.828064 34b0     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:27:06.828066 34b0     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:27:06.828068 34b0            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:27:06.828071 34b0                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:27:06.828073 34b0               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:27:06.828075 34b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.828077 34b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.828080 34b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:27:06.828082 34b0                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:27:06.828084 34b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.828087 34b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.828089 34b0            Options.compression_opts.window_bits: -14
2025/06/19-20:27:06.828091 34b0                  Options.compression_opts.level: 32767
2025/06/19-20:27:06.828093 34b0               Options.compression_opts.strategy: 0
2025/06/19-20:27:06.828096 34b0         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.828098 34b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.828100 34b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.828102 34b0         Options.compression_opts.parallel_threads: 1
2025/06/19-20:27:06.828105 34b0                  Options.compression_opts.enabled: false
2025/06/19-20:27:06.828107 34b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.828109 34b0      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:27:06.828112 34b0          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:27:06.828114 34b0              Options.level0_stop_writes_trigger: 40
2025/06/19-20:27:06.828116 34b0                   Options.target_file_size_base: 67108864
2025/06/19-20:27:06.828118 34b0             Options.target_file_size_multiplier: 1
2025/06/19-20:27:06.828121 34b0                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:27:06.828123 34b0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:27:06.828125 34b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:27:06.828128 34b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:27:06.828130 34b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:27:06.828132 34b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:27:06.828135 34b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:27:06.828137 34b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:27:06.828139 34b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:27:06.828141 34b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:27:06.828144 34b0       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:27:06.828146 34b0                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:27:06.828148 34b0                        Options.arena_block_size: 1048576
2025/06/19-20:27:06.828151 34b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:27:06.828153 34b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:27:06.828155 34b0                Options.disable_auto_compactions: 0
2025/06/19-20:27:06.828158 34b0                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:27:06.828161 34b0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:27:06.828175 34b0 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:27:06.828178 34b0 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:27:06.828181 34b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:27:06.828183 34b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:27:06.828185 34b0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:27:06.828188 34b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:27:06.828190 34b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:27:06.828193 34b0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:27:06.828198 34b0                   Options.table_properties_collectors: 
2025/06/19-20:27:06.828200 34b0                   Options.inplace_update_support: 0
2025/06/19-20:27:06.828202 34b0                 Options.inplace_update_num_locks: 10000
2025/06/19-20:27:06.828205 34b0               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:27:06.828208 34b0               Options.memtable_whole_key_filtering: 0
2025/06/19-20:27:06.828210 34b0   Options.memtable_huge_page_size: 0
2025/06/19-20:27:06.828212 34b0                           Options.bloom_locality: 0
2025/06/19-20:27:06.828214 34b0                    Options.max_successive_merges: 0
2025/06/19-20:27:06.828217 34b0                Options.optimize_filters_for_hits: 0
2025/06/19-20:27:06.828219 34b0                Options.paranoid_file_checks: 0
2025/06/19-20:27:06.828221 34b0                Options.force_consistency_checks: 1
2025/06/19-20:27:06.828224 34b0                Options.report_bg_io_stats: 0
2025/06/19-20:27:06.828226 34b0                               Options.ttl: 2592000
2025/06/19-20:27:06.828228 34b0          Options.periodic_compaction_seconds: 0
2025/06/19-20:27:06.828231 34b0  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:27:06.828233 34b0                       Options.enable_blob_files: false
2025/06/19-20:27:06.828235 34b0                           Options.min_blob_size: 0
2025/06/19-20:27:06.828237 34b0                          Options.blob_file_size: 268435456
2025/06/19-20:27:06.828240 34b0                   Options.blob_compression_type: NoCompression
2025/06/19-20:27:06.828242 34b0          Options.enable_blob_garbage_collection: false
2025/06/19-20:27:06.828245 34b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:27:06.828247 34b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:27:06.828250 34b0          Options.blob_compaction_readahead_size: 0
2025/06/19-20:27:06.828252 34b0                Options.blob_file_starting_level: 0
2025/06/19-20:27:06.828254 34b0 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:27:06.831886 34b0 [db\version_set.cc:5579] Recovered from manifest file:d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/MANIFEST-000025 succeeded,manifest_file_number is 25, next_file_number is 27, last_sequence is 9, log_number is 19,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 19
2025/06/19-20:27:06.831898 34b0 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 19
2025/06/19-20:27:06.831902 34b0 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 19
2025/06/19-20:27:06.832241 34b0 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9f-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-20:27:06.834213 34b0 EVENT_LOG_v1 {"time_micros": 1750336026834205, "job": 1, "event": "recovery_started", "wal_files": [24]}
2025/06/19-20:27:06.834222 34b0 [db\db_impl\db_impl_open.cc:1029] Recovering log #24 mode 2
2025/06/19-20:27:06.835982 34b0 EVENT_LOG_v1 {"time_micros": 1750336026835945, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 28, "file_size": 1184, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 10, "largest_seqno": 12, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750336026, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9f-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "Z9GJHPZR9V5DOT5YCAS0", "orig_file_number": 28, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:27:06.839917 34b0 EVENT_LOG_v1 {"time_micros": 1750336026839883, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 29, "file_size": 1190, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 11, "largest_seqno": 13, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750336026, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9f-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "Z9GJHPZR9V5DOT5YCAS0", "orig_file_number": 29, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:27:06.842950 34b0 EVENT_LOG_v1 {"time_micros": 1750336026842940, "job": 1, "event": "recovery_finished"}
2025/06/19-20:27:06.843544 34b0 [db\version_set.cc:5051] Creating manifest 31
2025/06/19-20:27:06.851938 34b0 [file\delete_scheduler.cc:77] Deleted file d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/000024.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-20:27:06.851974 34b0 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 0000018DC35BA780
2025/06/19-20:27:06.853190 34b0 DB pointer 0000018DC4456080

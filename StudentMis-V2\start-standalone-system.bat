@echo off
chcp 65001 >nul
echo ==========================================
echo   清华大学学生成绩管理系统 V2.0
echo   独立部署版本 - 无需Nacos注册中心
echo ==========================================

echo.
echo 正在检查环境...

:: 检查Java版本
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Java环境未正确配置
    pause
    exit /b 1
)

:: 检查Maven
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Maven环境未正确配置
    pause
    exit /b 1
)

:: 检查MySQL服务
netstat -an | findstr :3306 >nul
if %errorlevel% neq 0 (
    echo [警告] MySQL服务可能未启动，请确保MySQL正在运行
)

echo [信息] 环境检查完成
echo.

echo 正在清理并编译项目...
mvn clean install -DskipTests -q
if %errorlevel% neq 0 (
    echo [错误] 项目编译失败
    pause
    exit /b 1
)

echo [成功] 项目编译完成
echo.

echo 正在启动独立微服务...
echo.

:: 启动学生服务（已验证可用）
echo [启动] 学生管理服务 (端口8082) - 主要服务
start "StudentMIS-Student" cmd /k "title 学生管理服务-8082 && cd studentmis-student && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

:: 启动网关服务（简化版）
echo [启动] API网关服务 (端口8080) - 路由服务
start "StudentMIS-Gateway" cmd /k "title API网关服务-8080 && cd studentmis-gateway && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

echo.
echo ==========================================
echo   独立系统启动完成
echo   无需Nacos - 直接服务间通信
echo ==========================================
echo.
echo 核心服务地址：
echo   - 学生管理服务:  http://localhost:8082
echo   - API网关:      http://localhost:8080
echo.
echo 主要功能测试：
echo   curl http://localhost:8082/api/students/simple/test
echo   curl http://localhost:8082/api/students/simple/by-student-id/2024140520
echo   curl http://localhost:8082/api/students/simple
echo.
echo 您的学生数据：
echo   学号: 2024140520 (徐浩翔)
echo   专业: 计算机科学与技术专业（专转本）
echo   班级: 计算机4241班
echo.
echo API文档：
echo   http://localhost:8082/swagger-ui.html
echo.
echo 注意：此版本为独立部署，无需任何注册中心
echo      所有服务直接通过HTTP进行通信
echo.

pause

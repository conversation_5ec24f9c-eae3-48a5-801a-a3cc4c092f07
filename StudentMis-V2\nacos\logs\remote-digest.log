2025-06-19 20:14:44,315 INFO Connection transportReady,connectionId = 1750335284315_127.0.0.1_60342 

2025-06-19 20:14:46,962 INFO Connection transportTerminated,connectionId = 1750335284315_127.0.0.1_60342 

2025-06-19 20:14:46,963 INFO [1750335284315_127.0.0.1_60342]client disconnected,clear config listen context

2025-06-19 20:15:14,957 INFO Connection transportReady,connectionId = 1750335314957_127.0.0.1_60465 

2025-06-19 20:17:54,412 INFO Connection transportReady,connectionId = 1750335474412_127.0.0.1_60954 

2025-06-19 20:17:57,712 INFO Connection transportReady,connectionId = 1750335477712_127.0.0.1_60961 

2025-06-19 20:17:58,330 INFO Connection transportTerminated,connectionId = 1750335477712_127.0.0.1_60961 

2025-06-19 20:17:58,332 WARN [1750335477712_127.0.0.1_60961] connection  close exception  : {}

java.lang.IllegalStateException: call already closed
	at com.google.common.base.Preconditions.checkState(Preconditions.java:512)
	at io.grpc.internal.ServerCallImpl.closeInternal(ServerCallImpl.java:212)
	at io.grpc.internal.ServerCallImpl.close(ServerCallImpl.java:207)
	at io.grpc.stub.ServerCalls$ServerCallStreamObserverImpl.onCompleted(ServerCalls.java:395)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.closeBiStream(GrpcConnection.java:149)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.close(GrpcConnection.java:137)
	at com.alibaba.nacos.core.remote.ConnectionManager.unregister(ConnectionManager.java:159)
	at com.alibaba.nacos.core.remote.grpc.AddressTransportFilter.transportTerminated(AddressTransportFilter.java:77)
	at io.grpc.internal.ServerImpl$ServerTransportListenerImpl.transportTerminated(ServerImpl.java:456)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.notifyTerminated(NettyServerTransport.java:207)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.access$100(NettyServerTransport.java:51)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:141)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:134)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.closeOnRead(AbstractNioByteChannel.java:105)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:174)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-19 20:17:58,333 INFO [1750335477712_127.0.0.1_60961]client disconnected,clear config listen context

2025-06-19 20:18:11,133 INFO Connection transportTerminated,connectionId = 1750335474412_127.0.0.1_60954 

2025-06-19 20:18:11,135 INFO [1750335474412_127.0.0.1_60954]client disconnected,clear config listen context

2025-06-19 20:18:44,887 INFO Connection transportReady,connectionId = 1750335524887_127.0.0.1_61138 

2025-06-19 20:18:48,062 INFO Connection transportReady,connectionId = 1750335528062_127.0.0.1_61155 

2025-06-19 20:18:48,659 INFO Connection transportTerminated,connectionId = 1750335528062_127.0.0.1_61155 

2025-06-19 20:18:48,659 WARN [1750335528062_127.0.0.1_61155] connection  close exception  : {}

java.lang.IllegalStateException: call already closed
	at com.google.common.base.Preconditions.checkState(Preconditions.java:512)
	at io.grpc.internal.ServerCallImpl.closeInternal(ServerCallImpl.java:212)
	at io.grpc.internal.ServerCallImpl.close(ServerCallImpl.java:207)
	at io.grpc.stub.ServerCalls$ServerCallStreamObserverImpl.onCompleted(ServerCalls.java:395)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.closeBiStream(GrpcConnection.java:149)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.close(GrpcConnection.java:137)
	at com.alibaba.nacos.core.remote.ConnectionManager.unregister(ConnectionManager.java:159)
	at com.alibaba.nacos.core.remote.grpc.AddressTransportFilter.transportTerminated(AddressTransportFilter.java:77)
	at io.grpc.internal.ServerImpl$ServerTransportListenerImpl.transportTerminated(ServerImpl.java:456)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.notifyTerminated(NettyServerTransport.java:207)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.access$100(NettyServerTransport.java:51)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:141)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:134)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.closeOnRead(AbstractNioByteChannel.java:105)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:174)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-19 20:18:48,660 INFO [1750335528062_127.0.0.1_61155]client disconnected,clear config listen context

2025-06-19 20:18:59,446 INFO Connection transportTerminated,connectionId = 1750335524887_127.0.0.1_61138 

2025-06-19 20:18:59,447 INFO [1750335524887_127.0.0.1_61138]client disconnected,clear config listen context

2025-06-19 20:19:39,437 INFO Connection transportReady,connectionId = 1750335579437_127.0.0.1_61325 

2025-06-19 20:19:42,639 INFO Connection transportReady,connectionId = 1750335582639_127.0.0.1_61332 

2025-06-19 20:20:09,202 INFO Connection transportTerminated,connectionId = 1750335582639_127.0.0.1_61332 

2025-06-19 20:20:09,203 WARN [1750335582639_127.0.0.1_61332] connection  close exception  : {}

java.lang.IllegalStateException: call already closed
	at com.google.common.base.Preconditions.checkState(Preconditions.java:512)
	at io.grpc.internal.ServerCallImpl.closeInternal(ServerCallImpl.java:212)
	at io.grpc.internal.ServerCallImpl.close(ServerCallImpl.java:207)
	at io.grpc.stub.ServerCalls$ServerCallStreamObserverImpl.onCompleted(ServerCalls.java:395)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.closeBiStream(GrpcConnection.java:149)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.close(GrpcConnection.java:137)
	at com.alibaba.nacos.core.remote.ConnectionManager.unregister(ConnectionManager.java:159)
	at com.alibaba.nacos.core.remote.grpc.AddressTransportFilter.transportTerminated(AddressTransportFilter.java:77)
	at io.grpc.internal.ServerImpl$ServerTransportListenerImpl.transportTerminated(ServerImpl.java:456)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.notifyTerminated(NettyServerTransport.java:207)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.access$100(NettyServerTransport.java:51)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:141)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:134)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.closeOnRead(AbstractNioByteChannel.java:105)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:174)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-19 20:20:09,203 INFO [1750335582639_127.0.0.1_61332]client disconnected,clear config listen context

2025-06-19 20:20:09,728 INFO Connection transportTerminated,connectionId = 1750335579437_127.0.0.1_61325 

2025-06-19 20:20:09,729 INFO [1750335579437_127.0.0.1_61325]client disconnected,clear config listen context

2025-06-19 20:21:33,765 INFO Connection transportReady,connectionId = 1750335693765_127.0.0.1_61658 

2025-06-19 20:21:35,508 INFO Connection transportTerminated,connectionId = 1750335693765_127.0.0.1_61658 

2025-06-19 20:21:35,508 INFO [1750335693765_127.0.0.1_61658]client disconnected,clear config listen context

2025-06-19 20:22:08,392 INFO Connection transportReady,connectionId = 1750335728392_127.0.0.1_61761 

2025-06-19 20:22:10,095 INFO Connection transportTerminated,connectionId = 1750335728392_127.0.0.1_61761 

2025-06-19 20:22:10,095 INFO [1750335728392_127.0.0.1_61761]client disconnected,clear config listen context

2025-06-19 20:23:09,885 INFO Connection transportReady,connectionId = 1750335789885_127.0.0.1_61961 

2025-06-19 20:23:13,063 INFO Connection transportReady,connectionId = 1750335793063_127.0.0.1_61971 

2025-06-19 20:23:26,189 INFO Connection transportTerminated,connectionId = 1750335793063_127.0.0.1_61971 

2025-06-19 20:23:26,189 WARN [1750335793063_127.0.0.1_61971] connection  close exception  : {}

java.lang.IllegalStateException: call already closed
	at com.google.common.base.Preconditions.checkState(Preconditions.java:512)
	at io.grpc.internal.ServerCallImpl.closeInternal(ServerCallImpl.java:212)
	at io.grpc.internal.ServerCallImpl.close(ServerCallImpl.java:207)
	at io.grpc.stub.ServerCalls$ServerCallStreamObserverImpl.onCompleted(ServerCalls.java:395)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.closeBiStream(GrpcConnection.java:149)
	at com.alibaba.nacos.core.remote.grpc.GrpcConnection.close(GrpcConnection.java:137)
	at com.alibaba.nacos.core.remote.ConnectionManager.unregister(ConnectionManager.java:159)
	at com.alibaba.nacos.core.remote.grpc.AddressTransportFilter.transportTerminated(AddressTransportFilter.java:77)
	at io.grpc.internal.ServerImpl$ServerTransportListenerImpl.transportTerminated(ServerImpl.java:456)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.notifyTerminated(NettyServerTransport.java:207)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport.access$100(NettyServerTransport.java:51)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:141)
	at io.grpc.netty.shaded.io.grpc.netty.NettyServerTransport$1TerminationNotifier.operationComplete(NettyServerTransport.java:134)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at io.grpc.netty.shaded.io.netty.channel.DefaultChannelPromise.trySuccess(DefaultChannelPromise.java:84)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$CloseFuture.setClosed(AbstractChannel.java:1164)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.doClose0(AbstractChannel.java:755)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:731)
	at io.grpc.netty.shaded.io.netty.channel.AbstractChannel$AbstractUnsafe.close(AbstractChannel.java:620)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.closeOnRead(AbstractNioByteChannel.java:105)
	at io.grpc.netty.shaded.io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:174)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.grpc.netty.shaded.io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.grpc.netty.shaded.io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.grpc.netty.shaded.io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-19 20:23:26,189 INFO [1750335793063_127.0.0.1_61971]client disconnected,clear config listen context

2025-06-19 20:23:26,719 INFO Connection transportTerminated,connectionId = 1750335789885_127.0.0.1_61961 

2025-06-19 20:23:26,720 INFO [1750335789885_127.0.0.1_61961]client disconnected,clear config listen context

2025-06-19 20:24:16,341 INFO Connection transportReady,connectionId = 1750335856341_127.0.0.1_62142 

2025-06-19 20:24:19,633 INFO Connection transportReady,connectionId = 1750335859633_127.0.0.1_62146 

2025-06-19 20:28:05,323 INFO Connection transportReady,connectionId = 1750336085323_127.0.0.1_62547 

2025-06-19 20:28:08,267 INFO Connection transportReady,connectionId = 1750336088267_127.0.0.1_62554 

2025-06-19 20:29:50,122 INFO Connection transportReady,connectionId = 1750336190122_127.0.0.1_62852 

2025-06-19 20:29:51,821 INFO Connection transportTerminated,connectionId = 1750336190122_127.0.0.1_62852 

2025-06-19 20:29:51,823 INFO [1750336190122_127.0.0.1_62852]client disconnected,clear config listen context

2025-06-19 20:30:15,904 INFO Connection transportReady,connectionId = 1750336215904_127.0.0.1_62931 

2025-06-19 20:30:17,568 INFO Connection transportTerminated,connectionId = 1750336215904_127.0.0.1_62931 

2025-06-19 20:30:17,568 INFO [1750336215904_127.0.0.1_62931]client disconnected,clear config listen context

2025-06-19 20:33:10,727 INFO Connection transportReady,connectionId = 1750336390727_127.0.0.1_63523 

2025-06-19 20:33:12,403 INFO Connection transportTerminated,connectionId = 1750336390727_127.0.0.1_63523 

2025-06-19 20:33:12,404 INFO [1750336390727_127.0.0.1_63523]client disconnected,clear config listen context

2025-06-19 20:37:11,013 INFO Connection transportReady,connectionId = 1750336631013_127.0.0.1_64322 

2025-06-19 20:37:12,641 INFO Connection transportTerminated,connectionId = 1750336631013_127.0.0.1_64322 

2025-06-19 20:37:12,641 INFO [1750336631013_127.0.0.1_64322]client disconnected,clear config listen context


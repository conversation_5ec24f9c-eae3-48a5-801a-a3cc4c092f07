package edu.tsinghua.studentmis.common.config;

// MyBatis Plus配置已移除，改用标准MyBatis配置
// 各服务需要自己配置MyBatis相关设置

/**
 * MyBatis配置 - 已禁用
 *
 * <AUTHOR> Team
 * @since 2.0.0
 */
// @Configuration
// @Slf4j
public class MybatisPlusConfig {

    // MyBatis Plus配置已移除，改用标准MyBatis配置

    /*
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));

        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        return interceptor;
    }

    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new CustomMetaObjectHandler();
    }

    public static class CustomMetaObjectHandler implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            log.debug("开始插入填充...");

            // 填充创建时间
            this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());
            this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());

            // 填充创建人和更新人
            Long currentUserId = getCurrentUserId();
            this.strictInsertFill(metaObject, "createdBy", Long.class, currentUserId);
            this.strictInsertFill(metaObject, "updatedBy", Long.class, currentUserId);

            // 填充版本号
            this.strictInsertFill(metaObject, "version", Integer.class, 1);

            // 填充逻辑删除标识
            this.strictInsertFill(metaObject, "deleted", Boolean.class, false);
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            log.debug("开始更新填充...");

            // 填充更新时间
            this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());

            // 填充更新人
            Long currentUserId = getCurrentUserId();
            this.strictUpdateFill(metaObject, "updatedBy", Long.class, currentUserId);
        }

        private Long getCurrentUserId() {
            try {
                return SecurityUtils.getCurrentUserId();
            } catch (Exception e) {
                log.warn("获取当前用户ID失败，使用默认值: {}", e.getMessage());
                return 1L; // 默认系统用户ID
            }
        }
    }
    */
}

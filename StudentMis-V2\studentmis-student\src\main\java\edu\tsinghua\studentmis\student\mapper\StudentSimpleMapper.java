package edu.tsinghua.studentmis.student.mapper;

import edu.tsinghua.studentmis.student.entity.StudentSimple;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 学生信息Mapper接口（简化版）
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface StudentSimpleMapper {

    /**
     * 查询所有学生
     */
    @Select("SELECT id, student_id as studentId, name, name_en as nameEn, gender, birth_date as birthDate, phone, email, admission_date as admissionDate, status, major_id as majorId, class_id as classId FROM stu_basic_info ORDER BY student_id LIMIT 20")
    List<StudentSimple> selectAll();

    /**
     * 根据ID查询学生
     */
    @Select("SELECT id, student_id as studentId, name, name_en as nameEn, gender, birth_date as birthDate, phone, email, admission_date as admissionDate, status, major_id as majorId, class_id as classId FROM stu_basic_info WHERE id = #{id}")
    StudentSimple selectById(Long id);

    /**
     * 根据学号查询学生
     */
    @Select("SELECT id, student_id as studentId, name, name_en as nameEn, gender, birth_date as birthDate, phone, email, admission_date as admissionDate, status, major_id as majorId, class_id as classId FROM stu_basic_info WHERE student_id = #{studentId}")
    StudentSimple selectByStudentId(String studentId);
}

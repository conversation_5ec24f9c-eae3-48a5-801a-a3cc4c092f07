# StudentMIS V2 手动启动命令集合
# 清华大学级学生成绩管理系统

## 方式一：使用批处理文件（推荐）
双击运行：启动命令.bat

## 方式二：手动逐个启动服务

### 1. 启动认证服务 (端口8081)
cd /d D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\studentmis-auth
mvn spring-boot:run

### 2. 启动学生服务 (端口8082)  
cd /d D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\studentmis-student
mvn spring-boot:run

### 3. 启动成绩服务 (端口8083)
cd /d D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\studentmis-grade
mvn spring-boot:run

### 4. 启动数据分析服务 (端口8084)
cd /d D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\studentmis-analytics
mvn spring-boot:run

## 方式三：一键启动所有服务（新开4个CMD窗口）
start "Auth" cmd /k "cd /d D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\studentmis-auth && mvn spring-boot:run"
start "Student" cmd /k "cd /d D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\studentmis-student && mvn spring-boot:run"  
start "Grade" cmd /k "cd /d D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\studentmis-grade && mvn spring-boot:run"
start "Analytics" cmd /k "cd /d D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\studentmis-analytics && mvn spring-boot:run"

## 服务访问地址
- 认证服务:     http://localhost:8081
- 学生服务:     http://localhost:8082
- 成绩服务:     http://localhost:8083  
- 数据分析服务: http://localhost:8084

## 健康检查地址
- 认证服务健康检查:     http://localhost:8081/actuator/health
- 学生服务健康检查:     http://localhost:8082/actuator/health
- 成绩服务健康检查:     http://localhost:8083/actuator/health
- 数据分析服务健康检查: http://localhost:8084/actuator/health

## 停止服务
在各个服务的CMD窗口中按 Ctrl+C 停止对应服务

## 注意事项
1. 确保MySQL服务已启动
2. 确保Redis服务已启动  
3. 确保Java 17已正确安装
4. 建议按顺序启动：认证服务 -> 学生服务 -> 成绩服务 -> 数据分析服务
5. 每个服务启动需要20-30秒，请耐心等待

2025/06/19-20:17:27.812326 5278 RocksDB version: 7.7.3
2025/06/19-20:17:27.812392 5278 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-20:17:27.812402 5278 Compile date 2022-10-24 17:17:55
2025/06/19-20:17:27.812416 5278 DB SUMMARY
2025/06/19-20:17:27.812423 5278 DB Session ID:  EI1DCCUQWNAYVIOM60QA
2025/06/19-20:17:27.813025 5278 CURRENT file:  CURRENT
2025/06/19-20:17:27.813035 5278 IDENTITY file:  IDENTITY
2025/06/19-20:17:27.813113 5278 MANIFEST file:  MANIFEST-000019 size: 524 Bytes
2025/06/19-20:17:27.813128 5278 SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log dir, Total Num: 4, files: 000010.sst 000011.sst 000016.sst 000017.sst 
2025/06/19-20:17:27.813139 5278 Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log: 000018.log size: 187 ; 
2025/06/19-20:17:27.813254 5278                         Options.error_if_exists: 0
2025/06/19-20:17:27.813259 5278                       Options.create_if_missing: 1
2025/06/19-20:17:27.813261 5278                         Options.paranoid_checks: 1
2025/06/19-20:17:27.813263 5278             Options.flush_verify_memtable_count: 1
2025/06/19-20:17:27.813265 5278                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-20:17:27.813268 5278        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-20:17:27.813270 5278                                     Options.env: 000001EA6A04C950
2025/06/19-20:17:27.813272 5278                                      Options.fs: WinFS
2025/06/19-20:17:27.813275 5278                                Options.info_log: 000001EA6957F390
2025/06/19-20:17:27.813277 5278                Options.max_file_opening_threads: 16
2025/06/19-20:17:27.813279 5278                              Options.statistics: 000001EA689E0F00
2025/06/19-20:17:27.813281 5278                               Options.use_fsync: 0
2025/06/19-20:17:27.813284 5278                       Options.max_log_file_size: 0
2025/06/19-20:17:27.813286 5278                  Options.max_manifest_file_size: 1073741824
2025/06/19-20:17:27.813288 5278                   Options.log_file_time_to_roll: 0
2025/06/19-20:17:27.813290 5278                       Options.keep_log_file_num: 100
2025/06/19-20:17:27.813292 5278                    Options.recycle_log_file_num: 0
2025/06/19-20:17:27.813295 5278                         Options.allow_fallocate: 1
2025/06/19-20:17:27.813297 5278                        Options.allow_mmap_reads: 0
2025/06/19-20:17:27.813299 5278                       Options.allow_mmap_writes: 0
2025/06/19-20:17:27.813301 5278                        Options.use_direct_reads: 0
2025/06/19-20:17:27.813303 5278                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-20:17:27.813305 5278          Options.create_missing_column_families: 1
2025/06/19-20:17:27.813307 5278                              Options.db_log_dir: 
2025/06/19-20:17:27.813310 5278                                 Options.wal_dir: 
2025/06/19-20:17:27.813312 5278                Options.table_cache_numshardbits: 6
2025/06/19-20:17:27.813316 5278                         Options.WAL_ttl_seconds: 0
2025/06/19-20:17:27.813318 5278                       Options.WAL_size_limit_MB: 0
2025/06/19-20:17:27.813320 5278                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-20:17:27.813322 5278             Options.manifest_preallocation_size: 4194304
2025/06/19-20:17:27.813325 5278                     Options.is_fd_close_on_exec: 1
2025/06/19-20:17:27.813327 5278                   Options.advise_random_on_open: 1
2025/06/19-20:17:27.813329 5278                    Options.db_write_buffer_size: 0
2025/06/19-20:17:27.813331 5278                    Options.write_buffer_manager: 000001EA6A04CC20
2025/06/19-20:17:27.813333 5278         Options.access_hint_on_compaction_start: 1
2025/06/19-20:17:27.813336 5278           Options.random_access_max_buffer_size: 1048576
2025/06/19-20:17:27.813338 5278                      Options.use_adaptive_mutex: 0
2025/06/19-20:17:27.813356 5278                            Options.rate_limiter: 0000000000000000
2025/06/19-20:17:27.813360 5278     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-20:17:27.813363 5278                       Options.wal_recovery_mode: 2
2025/06/19-20:17:27.813365 5278                  Options.enable_thread_tracking: 0
2025/06/19-20:17:27.813367 5278                  Options.enable_pipelined_write: 0
2025/06/19-20:17:27.813369 5278                  Options.unordered_write: 0
2025/06/19-20:17:27.813371 5278         Options.allow_concurrent_memtable_write: 1
2025/06/19-20:17:27.813373 5278      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-20:17:27.813375 5278             Options.write_thread_max_yield_usec: 100
2025/06/19-20:17:27.813377 5278            Options.write_thread_slow_yield_usec: 3
2025/06/19-20:17:27.813380 5278                               Options.row_cache: None
2025/06/19-20:17:27.813382 5278                              Options.wal_filter: None
2025/06/19-20:17:27.813384 5278             Options.avoid_flush_during_recovery: 0
2025/06/19-20:17:27.813386 5278             Options.allow_ingest_behind: 0
2025/06/19-20:17:27.813388 5278             Options.two_write_queues: 0
2025/06/19-20:17:27.813390 5278             Options.manual_wal_flush: 0
2025/06/19-20:17:27.813392 5278             Options.wal_compression: 0
2025/06/19-20:17:27.813395 5278             Options.atomic_flush: 0
2025/06/19-20:17:27.813397 5278             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-20:17:27.813399 5278                 Options.persist_stats_to_disk: 0
2025/06/19-20:17:27.813401 5278                 Options.write_dbid_to_manifest: 0
2025/06/19-20:17:27.813403 5278                 Options.log_readahead_size: 0
2025/06/19-20:17:27.813405 5278                 Options.file_checksum_gen_factory: Unknown
2025/06/19-20:17:27.813408 5278                 Options.best_efforts_recovery: 0
2025/06/19-20:17:27.813410 5278                Options.max_bgerror_resume_count: 2147483647
2025/06/19-20:17:27.813412 5278            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-20:17:27.813414 5278             Options.allow_data_in_errors: 0
2025/06/19-20:17:27.813416 5278             Options.db_host_id: __hostname__
2025/06/19-20:17:27.813418 5278             Options.enforce_single_del_contracts: true
2025/06/19-20:17:27.813420 5278             Options.max_background_jobs: 2
2025/06/19-20:17:27.813423 5278             Options.max_background_compactions: 4
2025/06/19-20:17:27.813425 5278             Options.max_subcompactions: 1
2025/06/19-20:17:27.813427 5278             Options.avoid_flush_during_shutdown: 0
2025/06/19-20:17:27.813429 5278           Options.writable_file_max_buffer_size: 1048576
2025/06/19-20:17:27.813431 5278             Options.delayed_write_rate : 16777216
2025/06/19-20:17:27.813433 5278             Options.max_total_wal_size: 1073741824
2025/06/19-20:17:27.813435 5278             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-20:17:27.813438 5278                   Options.stats_dump_period_sec: 600
2025/06/19-20:17:27.813440 5278                 Options.stats_persist_period_sec: 600
2025/06/19-20:17:27.813442 5278                 Options.stats_history_buffer_size: 1048576
2025/06/19-20:17:27.813444 5278                          Options.max_open_files: -1
2025/06/19-20:17:27.813447 5278                          Options.bytes_per_sync: 0
2025/06/19-20:17:27.813449 5278                      Options.wal_bytes_per_sync: 0
2025/06/19-20:17:27.813451 5278                   Options.strict_bytes_per_sync: 0
2025/06/19-20:17:27.813453 5278       Options.compaction_readahead_size: 0
2025/06/19-20:17:27.813455 5278                  Options.max_background_flushes: 1
2025/06/19-20:17:27.813458 5278 Compression algorithms supported:
2025/06/19-20:17:27.813465 5278 	kZSTD supported: 1
2025/06/19-20:17:27.813468 5278 	kSnappyCompression supported: 1
2025/06/19-20:17:27.813470 5278 	kBZip2Compression supported: 0
2025/06/19-20:17:27.813481 5278 	kZlibCompression supported: 1
2025/06/19-20:17:27.813484 5278 	kLZ4Compression supported: 1
2025/06/19-20:17:27.813486 5278 	kXpressCompression supported: 0
2025/06/19-20:17:27.813488 5278 	kLZ4HCCompression supported: 1
2025/06/19-20:17:27.813490 5278 	kZSTDNotFinalCompression supported: 1
2025/06/19-20:17:27.813496 5278 Fast CRC32 supported: Not supported on x86
2025/06/19-20:17:27.813499 5278 DMutex implementation: std::mutex
2025/06/19-20:17:27.814378 5278 [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000019
2025/06/19-20:17:27.814626 5278 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-20:17:27.814636 5278               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:17:27.814639 5278           Options.merge_operator: StringAppendOperator
2025/06/19-20:17:27.814641 5278        Options.compaction_filter: None
2025/06/19-20:17:27.814643 5278        Options.compaction_filter_factory: None
2025/06/19-20:17:27.814645 5278  Options.sst_partitioner_factory: None
2025/06/19-20:17:27.814647 5278         Options.memtable_factory: SkipListFactory
2025/06/19-20:17:27.814649 5278            Options.table_factory: BlockBasedTable
2025/06/19-20:17:27.814676 5278            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001EA6691D9A0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000001EA69D77BD0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:17:27.814679 5278        Options.write_buffer_size: 67108864
2025/06/19-20:17:27.814681 5278  Options.max_write_buffer_number: 3
2025/06/19-20:17:27.814683 5278          Options.compression: Snappy
2025/06/19-20:17:27.814685 5278                  Options.bottommost_compression: Disabled
2025/06/19-20:17:27.814688 5278       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:17:27.814690 5278   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:17:27.814692 5278             Options.num_levels: 7
2025/06/19-20:17:27.814694 5278        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:17:27.814696 5278     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:17:27.814698 5278     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:17:27.814700 5278            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:17:27.814703 5278                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:17:27.814705 5278               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:17:27.814707 5278         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.814709 5278         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.814711 5278         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:17:27.814713 5278                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:17:27.814717 5278         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.814720 5278         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.814722 5278            Options.compression_opts.window_bits: -14
2025/06/19-20:17:27.814724 5278                  Options.compression_opts.level: 32767
2025/06/19-20:17:27.814726 5278               Options.compression_opts.strategy: 0
2025/06/19-20:17:27.814728 5278         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.814730 5278         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.814732 5278         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.814734 5278         Options.compression_opts.parallel_threads: 1
2025/06/19-20:17:27.814737 5278                  Options.compression_opts.enabled: false
2025/06/19-20:17:27.814739 5278         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.814741 5278      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:17:27.814743 5278          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:17:27.814745 5278              Options.level0_stop_writes_trigger: 40
2025/06/19-20:17:27.814747 5278                   Options.target_file_size_base: 67108864
2025/06/19-20:17:27.814749 5278             Options.target_file_size_multiplier: 1
2025/06/19-20:17:27.814751 5278                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:17:27.814753 5278 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:17:27.814755 5278          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:17:27.814762 5278 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:17:27.814764 5278 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:17:27.814766 5278 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:17:27.814768 5278 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:17:27.814770 5278 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:17:27.814772 5278 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:17:27.814774 5278 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:17:27.814776 5278       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:17:27.814778 5278                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:17:27.814781 5278                        Options.arena_block_size: 1048576
2025/06/19-20:17:27.814783 5278   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:17:27.814785 5278   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:17:27.814787 5278                Options.disable_auto_compactions: 0
2025/06/19-20:17:27.814790 5278                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:17:27.814792 5278                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:17:27.814795 5278 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:17:27.814797 5278 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:17:27.814799 5278 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:17:27.814801 5278 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:17:27.814803 5278 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:17:27.814805 5278 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:17:27.814808 5278 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:17:27.814810 5278 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:17:27.814813 5278                   Options.table_properties_collectors: 
2025/06/19-20:17:27.814815 5278                   Options.inplace_update_support: 0
2025/06/19-20:17:27.814817 5278                 Options.inplace_update_num_locks: 10000
2025/06/19-20:17:27.814819 5278               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:17:27.814844 5278               Options.memtable_whole_key_filtering: 0
2025/06/19-20:17:27.814847 5278   Options.memtable_huge_page_size: 0
2025/06/19-20:17:27.814849 5278                           Options.bloom_locality: 0
2025/06/19-20:17:27.814851 5278                    Options.max_successive_merges: 0
2025/06/19-20:17:27.814853 5278                Options.optimize_filters_for_hits: 0
2025/06/19-20:17:27.814855 5278                Options.paranoid_file_checks: 0
2025/06/19-20:17:27.814857 5278                Options.force_consistency_checks: 1
2025/06/19-20:17:27.814859 5278                Options.report_bg_io_stats: 0
2025/06/19-20:17:27.814861 5278                               Options.ttl: 2592000
2025/06/19-20:17:27.814863 5278          Options.periodic_compaction_seconds: 0
2025/06/19-20:17:27.814865 5278  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:17:27.814867 5278                       Options.enable_blob_files: false
2025/06/19-20:17:27.814870 5278                           Options.min_blob_size: 0
2025/06/19-20:17:27.814872 5278                          Options.blob_file_size: 268435456
2025/06/19-20:17:27.814874 5278                   Options.blob_compression_type: NoCompression
2025/06/19-20:17:27.814876 5278          Options.enable_blob_garbage_collection: false
2025/06/19-20:17:27.814878 5278      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:17:27.814881 5278 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:17:27.814883 5278          Options.blob_compaction_readahead_size: 0
2025/06/19-20:17:27.814885 5278                Options.blob_file_starting_level: 0
2025/06/19-20:17:27.814887 5278 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:17:27.816578 5278 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-20:17:27.816591 5278               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:17:27.816594 5278           Options.merge_operator: StringAppendOperator
2025/06/19-20:17:27.816596 5278        Options.compaction_filter: None
2025/06/19-20:17:27.816598 5278        Options.compaction_filter_factory: None
2025/06/19-20:17:27.816600 5278  Options.sst_partitioner_factory: None
2025/06/19-20:17:27.816603 5278         Options.memtable_factory: SkipListFactory
2025/06/19-20:17:27.816605 5278            Options.table_factory: BlockBasedTable
2025/06/19-20:17:27.816632 5278            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001EA6691D9A0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000001EA69D77BD0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:17:27.816635 5278        Options.write_buffer_size: 67108864
2025/06/19-20:17:27.816637 5278  Options.max_write_buffer_number: 3
2025/06/19-20:17:27.816640 5278          Options.compression: Snappy
2025/06/19-20:17:27.816642 5278                  Options.bottommost_compression: Disabled
2025/06/19-20:17:27.816644 5278       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:17:27.816696 5278   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:17:27.816699 5278             Options.num_levels: 7
2025/06/19-20:17:27.816702 5278        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:17:27.816704 5278     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:17:27.816706 5278     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:17:27.816708 5278            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:17:27.816710 5278                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:17:27.816712 5278               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:17:27.816715 5278         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.816717 5278         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.816719 5278         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:17:27.816721 5278                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:17:27.816723 5278         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.816725 5278         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.816727 5278            Options.compression_opts.window_bits: -14
2025/06/19-20:17:27.816729 5278                  Options.compression_opts.level: 32767
2025/06/19-20:17:27.816732 5278               Options.compression_opts.strategy: 0
2025/06/19-20:17:27.816734 5278         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.816736 5278         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.816738 5278         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.816740 5278         Options.compression_opts.parallel_threads: 1
2025/06/19-20:17:27.816742 5278                  Options.compression_opts.enabled: false
2025/06/19-20:17:27.816744 5278         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.816746 5278      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:17:27.816749 5278          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:17:27.816751 5278              Options.level0_stop_writes_trigger: 40
2025/06/19-20:17:27.816754 5278                   Options.target_file_size_base: 67108864
2025/06/19-20:17:27.816757 5278             Options.target_file_size_multiplier: 1
2025/06/19-20:17:27.816759 5278                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:17:27.816761 5278 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:17:27.816763 5278          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:17:27.816766 5278 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:17:27.816769 5278 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:17:27.816771 5278 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:17:27.816773 5278 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:17:27.816775 5278 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:17:27.816777 5278 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:17:27.816779 5278 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:17:27.816781 5278       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:17:27.816784 5278                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:17:27.816786 5278                        Options.arena_block_size: 1048576
2025/06/19-20:17:27.816788 5278   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:17:27.816790 5278   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:17:27.816792 5278                Options.disable_auto_compactions: 0
2025/06/19-20:17:27.816795 5278                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:17:27.816798 5278                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:17:27.816801 5278 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:17:27.816805 5278 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:17:27.816807 5278 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:17:27.816809 5278 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:17:27.816812 5278 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:17:27.816815 5278 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:17:27.816817 5278 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:17:27.816819 5278 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:17:27.816825 5278                   Options.table_properties_collectors: 
2025/06/19-20:17:27.816827 5278                   Options.inplace_update_support: 0
2025/06/19-20:17:27.816829 5278                 Options.inplace_update_num_locks: 10000
2025/06/19-20:17:27.816831 5278               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:17:27.816834 5278               Options.memtable_whole_key_filtering: 0
2025/06/19-20:17:27.816836 5278   Options.memtable_huge_page_size: 0
2025/06/19-20:17:27.816838 5278                           Options.bloom_locality: 0
2025/06/19-20:17:27.816840 5278                    Options.max_successive_merges: 0
2025/06/19-20:17:27.816842 5278                Options.optimize_filters_for_hits: 0
2025/06/19-20:17:27.816844 5278                Options.paranoid_file_checks: 0
2025/06/19-20:17:27.816846 5278                Options.force_consistency_checks: 1
2025/06/19-20:17:27.816848 5278                Options.report_bg_io_stats: 0
2025/06/19-20:17:27.816851 5278                               Options.ttl: 2592000
2025/06/19-20:17:27.816853 5278          Options.periodic_compaction_seconds: 0
2025/06/19-20:17:27.816855 5278  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:17:27.816857 5278                       Options.enable_blob_files: false
2025/06/19-20:17:27.816859 5278                           Options.min_blob_size: 0
2025/06/19-20:17:27.816861 5278                          Options.blob_file_size: 268435456
2025/06/19-20:17:27.816863 5278                   Options.blob_compression_type: NoCompression
2025/06/19-20:17:27.816866 5278          Options.enable_blob_garbage_collection: false
2025/06/19-20:17:27.816868 5278      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:17:27.816870 5278 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:17:27.816872 5278          Options.blob_compaction_readahead_size: 0
2025/06/19-20:17:27.816876 5278                Options.blob_file_starting_level: 0
2025/06/19-20:17:27.816878 5278 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:17:27.820164 5278 [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000019 succeeded,manifest_file_number is 19, next_file_number is 21, last_sequence is 5, log_number is 13,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 13
2025/06/19-20:17:27.820177 5278 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 13
2025/06/19-20:17:27.820182 5278 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 13
2025/06/19-20:17:27.820511 5278 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9d-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-20:17:27.822068 5278 EVENT_LOG_v1 {"time_micros": 1750335447822049, "job": 1, "event": "recovery_started", "wal_files": [18]}
2025/06/19-20:17:27.822082 5278 [db\db_impl\db_impl_open.cc:1029] Recovering log #18 mode 2
2025/06/19-20:17:27.824078 5278 EVENT_LOG_v1 {"time_micros": 1750335447824042, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 22, "file_size": 1184, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 6, "largest_seqno": 8, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335447, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9d-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "EI1DCCUQWNAYVIOM60QA", "orig_file_number": 22, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:17:27.827639 5278 EVENT_LOG_v1 {"time_micros": 1750335447827609, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 23, "file_size": 1190, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 7, "largest_seqno": 9, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335447, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9d-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "EI1DCCUQWNAYVIOM60QA", "orig_file_number": 23, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:17:27.830090 5278 EVENT_LOG_v1 {"time_micros": 1750335447830084, "job": 1, "event": "recovery_finished"}
2025/06/19-20:17:27.830575 5278 [db\version_set.cc:5051] Creating manifest 25
2025/06/19-20:17:27.837582 5278 [file\delete_scheduler.cc:77] Deleted file D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/000018.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-20:17:27.837669 5278 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 000001EA6A6C4050
2025/06/19-20:17:27.838248 5278 DB pointer 000001EA65822280
2025/06/19-20:17:27.838788 1ce0 [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-20:17:27.838803 1ce0 [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.34 KB   0.3      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      3/0    3.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000001EA69D77BD0#8752 capacity: 512.00 MB usage: 1.52 KB table_size: 4096 occupancy: 9 collections: 1 last_copies: 1 last_secs: 0.000125 secs_since: 0
Block cache entry stats(count,size,portion): IndexBlock(6,0.55 KB,0.000104681%) OtherBlock(2,0.21 KB,3.98606e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.41 KB   0.3      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      3/0    3.41 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000001EA69D77BD0#8752 capacity: 512.00 MB usage: 1.52 KB table_size: 4096 occupancy: 9 collections: 1 last_copies: 1 last_secs: 0.000125 secs_since: 0
Block cache entry stats(count,size,portion): IndexBlock(6,0.55 KB,0.000104681%) OtherBlock(2,0.21 KB,3.98606e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 6 Average: 20.3333  StdDev: 11.64
Min: 9  Median: 15.0000  Max: 39
Percentiles: P50: 15.00 P75: 28.00 P99: 39.00 P99.9: 39.00 P99.99: 39.00
------------------------------------------------------
(       6,      10 ]        1  16.667%  16.667% ###
(      10,      15 ]        2  33.333%  50.000% #######
(      15,      22 ]        1  16.667%  66.667% ###
(      22,      34 ]        1  16.667%  83.333% ###
(      34,      51 ]        1  16.667% 100.000% ###


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 6 Average: 13.5000  StdDev: 5.62
Min: 6  Median: 13.3333  Max: 22
Percentiles: P50: 13.33 P75: 16.75 P99: 21.79 P99.9: 21.98 P99.99: 22.00
------------------------------------------------------
(       4,       6 ]        1  16.667%  16.667% ###
(      10,      15 ]        3  50.000%  66.667% ##########
(      15,      22 ]        2  33.333% 100.000% #######

2025/06/19-20:17:27.839441 1ce0 [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 8
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 8
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 6
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 6
rocksdb.block.cache.index.bytes.insert COUNT : 562
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 2
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 2
rocksdb.block.cache.data.bytes.insert COUNT : 214
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 776
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 0
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 0
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 0
rocksdb.number.db.next COUNT : 0
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 0
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 0
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 0
rocksdb.wal.bytes COUNT : 0
rocksdb.write.self COUNT : 0
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 0
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2374
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 0
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 0
rocksdb.num.iterator.deleted COUNT : 0
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 13824
rocksdb.non.last.level.read.count COUNT : 12
rocksdb.block.checksum.compute.count COUNT : 20
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 480.000000 P95 : 524.000000 P99 : 524.000000 P100 : 524.000000 COUNT : 2 SUM : 969
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.manifest.file.sync.micros P50 : 353.000000 P95 : 353.000000 P99 : 353.000000 P100 : 353.000000 COUNT : 1 SUM : 353
rocksdb.table.open.io.micros P50 : 110.000000 P95 : 471.000000 P99 : 471.000000 P100 : 471.000000 COUNT : 6 SUM : 1329
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.583333 P95 : 3.300000 P99 : 3.860000 P100 : 4.000000 COUNT : 14 SUM : 14
rocksdb.write.raw.block.micros P50 : 0.666667 P95 : 12.000000 P99 : 14.000000 P100 : 14.000000 COUNT : 12 SUM : 21
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 14.000000 P95 : 39.000000 P99 : 39.000000 P100 : 39.000000 COUNT : 12 SUM : 203
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1085.000000 P95 : 1099.000000 P99 : 1099.000000 P100 : 1099.000000 COUNT : 6 SUM : 6319
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-20:17:28.846998 1ce0 [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-20:17:28.847022 1ce0 [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-20:17:28.847025 1ce0 [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0

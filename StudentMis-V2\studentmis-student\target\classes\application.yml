server:
  port: 8082

spring:
  application:
    name: studentmis-student
  profiles:
    active: dev
  # 临时禁用Nacos配置进行诊断
  # config:
  #   import: "optional:nacos:studentmis-student.yml"
  cloud:
    nacos:
      config:
        import-check:
          enabled: false
  #     discovery:
  #       server-addr: localhost:8848
  #       group: DEFAULT_GROUP
  #     config:
  #       server-addr: localhost:8848
  #       group: DEFAULT_GROUP
  #       file-extension: yml
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: root
    password: xhxabc
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 10000ms
      connect-timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
        shutdown-timeout: 100ms

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    edu.tsinghua.studentmis: debug

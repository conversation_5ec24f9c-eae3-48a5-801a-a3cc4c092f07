@echo off
chcp 65001 >nul
echo ==========================================
echo   清华大学学生成绩管理系统 V2.0
echo   完整系统启动 - 无需Nacos注册中心
echo ==========================================

echo.
echo 正在检查环境...

:: 检查Java版本
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Java环境未正确配置
    pause
    exit /b 1
)

:: 检查Maven
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Maven环境未正确配置
    pause
    exit /b 1
)

:: 检查MySQL服务
netstat -an | findstr :3306 >nul
if %errorlevel% neq 0 (
    echo [警告] MySQL服务可能未启动，请确保MySQL正在运行
)

echo [信息] 环境检查完成
echo.

echo 正在清理并编译整个项目...
mvn clean install -DskipTests -q
if %errorlevel% neq 0 (
    echo [错误] 项目编译失败
    pause
    exit /b 1
)

echo [成功] 项目编译完成
echo.

echo 正在启动完整微服务系统...
echo 注意：此版本已完全移除Nacos依赖，服务间直接通信
echo.

:: 启动API网关服务
echo [启动] API网关服务 (端口8080) - 系统入口
start "StudentMIS-Gateway" cmd /k "title API网关-8080 && cd studentmis-gateway && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

:: 启动认证服务
echo [启动] 认证授权服务 (端口8081) - 用户认证
start "StudentMIS-Auth" cmd /k "title 认证服务-8081 && cd studentmis-auth && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

:: 启动学生服务
echo [启动] 学生管理服务 (端口8082) - 核心业务
start "StudentMIS-Student" cmd /k "title 学生服务-8082 && cd studentmis-student && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

:: 启动成绩服务
echo [启动] 成绩管理服务 (端口8083) - 成绩业务
start "StudentMIS-Grade" cmd /k "title 成绩服务-8083 && cd studentmis-grade && mvn spring-boot:run"
timeout /t 5 /nobreak >nul

:: 启动分析服务
echo [启动] 数据分析服务 (端口8084) - 统计分析
start "StudentMIS-Analytics" cmd /k "title 分析服务-8084 && cd studentmis-analytics && mvn spring-boot:run"

echo.
echo ==========================================
echo   完整系统启动完成
echo   清华大学级学生成绩管理系统 V2.0
echo ==========================================
echo.
echo 系统架构：
echo   - API网关:      http://localhost:8080 (系统入口)
echo   - 认证服务:      http://localhost:8081 (用户认证)
echo   - 学生服务:      http://localhost:8082 (学生管理)
echo   - 成绩服务:      http://localhost:8083 (成绩管理)
echo   - 分析服务:      http://localhost:8084 (数据分析)
echo.
echo 核心功能测试：
echo   curl http://localhost:8082/api/students/simple/test
echo   curl http://localhost:8082/api/students/simple/by-student-id/2024140520
echo   curl http://localhost:8082/api/students/simple
echo.
echo 您的学生数据：
echo   学号: 2024140520 (徐浩翔)
echo   专业: 计算机科学与技术专业（专转本）
echo   班级: 计算机4241班
echo   同班同学: 王梓齐、张庆祥、蔡家璇、刘川东、沈磊、程学峰
echo.
echo API文档访问：
echo   http://localhost:8080/swagger-ui.html (网关统一入口)
echo   http://localhost:8082/swagger-ui.html (学生服务直接访问)
echo.
echo 系统特点：
echo   ✅ 完全无需Nacos注册中心
echo   ✅ 独立微服务架构
echo   ✅ 中文化界面和响应
echo   ✅ 真实学生数据
echo   ✅ 企业级架构设计
echo.
echo 请等待所有服务完全启动（约3-5分钟）
echo 看到所有窗口显示"Started Application"即表示启动成功
echo.

pause

package edu.tsinghua.studentmis.student.entity;

// import com.baomidou.mybatisplus.annotation.TableField;
// import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
// import edu.tsinghua.studentmis.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
// import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 学生基本信息实体
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
// @EqualsAndHashCode(callSuper = true)
// @TableName("stu_basic_info")
@Schema(description = "学生基本信息")
public class Student {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "学号")
    @NotBlank(message = "学号不能为空")
    @Pattern(regexp = "^[0-9]{8,12}$", message = "学号格式不正确")
    // @TableField("student_id")
    private String studentId;

    @Schema(description = "姓名")
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    // @TableField("name")
    private String name;

    @Schema(description = "英文姓名")
    @Size(max = 100, message = "英文姓名长度不能超过100个字符")
    // @TableField("name_en")
    private String nameEn;

    @Schema(description = "性别")
    @NotNull(message = "性别不能为空")
    // @TableField("gender")
    private Gender gender;

    @Schema(description = "出生日期")
    @NotNull(message = "出生日期不能为空")
    @Past(message = "出生日期必须是过去的日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    // @TableField("birth_date")
    private LocalDate birthDate;

    @Schema(description = "身份证号")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    @TableField("id_card")
    private String idCard;

    @Schema(description = "国籍")
    @Size(max = 50, message = "国籍长度不能超过50个字符")
    @TableField("nationality")
    private String nationality;

    @Schema(description = "民族")
    @Size(max = 20, message = "民族长度不能超过20个字符")
    @TableField("ethnicity")
    private String ethnicity;

    @Schema(description = "政治面貌")
    @Size(max = 20, message = "政治面貌长度不能超过20个字符")
    @TableField("political_status")
    private String politicalStatus;

    @Schema(description = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @TableField("phone")
    private String phone;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    @TableField("email")
    private String email;

    @Schema(description = "紧急联系人")
    @Size(max = 50, message = "紧急联系人姓名长度不能超过50个字符")
    @TableField("emergency_contact")
    private String emergencyContact;

    @Schema(description = "紧急联系电话")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "紧急联系电话格式不正确")
    @TableField("emergency_phone")
    private String emergencyPhone;

    @Schema(description = "入学日期")
    @NotNull(message = "入学日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("admission_date")
    private LocalDate admissionDate;

    @Schema(description = "毕业日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("graduation_date")
    private LocalDate graduationDate;

    @Schema(description = "学籍状态")
    @NotNull(message = "学籍状态不能为空")
    @TableField("status")
    private StudentStatus status;

    @Schema(description = "专业ID")
    @NotNull(message = "专业ID不能为空")
    @TableField("major_id")
    private Long majorId;

    @Schema(description = "班级ID")
    @NotNull(message = "班级ID不能为空")
    @TableField("class_id")
    private Long classId;

    @Schema(description = "宿舍")
    @Size(max = 50, message = "宿舍信息长度不能超过50个字符")
    @TableField("dormitory")
    private String dormitory;

    @Schema(description = "照片URL")
    @TableField("photo_url")
    private String photoUrl;

    @Schema(description = "备注")
    @TableField("remarks")
    private String remarks;

    /**
     * 性别枚举
     */
    public enum Gender {
        MALE("男"),
        FEMALE("女"),
        OTHER("其他");

        private final String description;

        Gender(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 学籍状态枚举
     */
    public enum StudentStatus {
        ACTIVE("在读"),
        SUSPENDED("休学"),
        GRADUATED("已毕业"),
        DROPPED("退学"),
        TRANSFERRED("转学");

        private final String description;

        StudentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查学生是否在读
     */
    public boolean isActive() {
        return StudentStatus.ACTIVE.equals(this.status);
    }

    /**
     * 检查学生是否已毕业
     */
    public boolean isGraduated() {
        return StudentStatus.GRADUATED.equals(this.status);
    }

    /**
     * 计算年龄
     */
    public int getAge() {
        if (birthDate == null) {
            return 0;
        }
        return LocalDate.now().getYear() - birthDate.getYear();
    }

    /**
     * 计算在校年限
     */
    public int getSchoolYears() {
        if (admissionDate == null) {
            return 0;
        }
        LocalDate endDate = graduationDate != null ? graduationDate : LocalDate.now();
        return endDate.getYear() - admissionDate.getYear();
    }
}

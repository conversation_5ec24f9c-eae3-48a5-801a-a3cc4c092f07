2025/06/19-20:17:27.950072 5278 RocksDB version: 7.7.3
2025/06/19-20:17:27.950177 5278 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-20:17:27.950198 5278 Compile date 2022-10-24 17:17:55
2025/06/19-20:17:27.950211 5278 DB SUMMARY
2025/06/19-20:17:27.950224 5278 DB Session ID:  EI1DCCUQWNAYVIOM60Q8
2025/06/19-20:17:27.951325 5278 CURRENT file:  CURRENT
2025/06/19-20:17:27.951345 5278 IDENTITY file:  IDENTITY
2025/06/19-20:17:27.951481 5278 MANIFEST file:  MANIFEST-000019 size: 524 Bytes
2025/06/19-20:17:27.951496 5278 SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log dir, Total Num: 4, files: 000010.sst 000011.sst 000016.sst 000017.sst 
2025/06/19-20:17:27.951509 5278 Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log: 000018.log size: 187 ; 
2025/06/19-20:17:27.951696 5278                         Options.error_if_exists: 0
2025/06/19-20:17:27.951722 5278                       Options.create_if_missing: 1
2025/06/19-20:17:27.951725 5278                         Options.paranoid_checks: 1
2025/06/19-20:17:27.951729 5278             Options.flush_verify_memtable_count: 1
2025/06/19-20:17:27.951732 5278                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-20:17:27.951736 5278        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-20:17:27.951740 5278                                     Options.env: 000001EA6A04C950
2025/06/19-20:17:27.951743 5278                                      Options.fs: WinFS
2025/06/19-20:17:27.951746 5278                                Options.info_log: 000001EA6465A430
2025/06/19-20:17:27.951749 5278                Options.max_file_opening_threads: 16
2025/06/19-20:17:27.951752 5278                              Options.statistics: 000001EA689D44E0
2025/06/19-20:17:27.951754 5278                               Options.use_fsync: 0
2025/06/19-20:17:27.951757 5278                       Options.max_log_file_size: 0
2025/06/19-20:17:27.951760 5278                  Options.max_manifest_file_size: 1073741824
2025/06/19-20:17:27.951763 5278                   Options.log_file_time_to_roll: 0
2025/06/19-20:17:27.951765 5278                       Options.keep_log_file_num: 100
2025/06/19-20:17:27.951768 5278                    Options.recycle_log_file_num: 0
2025/06/19-20:17:27.951770 5278                         Options.allow_fallocate: 1
2025/06/19-20:17:27.951773 5278                        Options.allow_mmap_reads: 0
2025/06/19-20:17:27.951776 5278                       Options.allow_mmap_writes: 0
2025/06/19-20:17:27.951778 5278                        Options.use_direct_reads: 0
2025/06/19-20:17:27.951781 5278                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-20:17:27.951826 5278          Options.create_missing_column_families: 1
2025/06/19-20:17:27.951832 5278                              Options.db_log_dir: 
2025/06/19-20:17:27.951835 5278                                 Options.wal_dir: 
2025/06/19-20:17:27.951839 5278                Options.table_cache_numshardbits: 6
2025/06/19-20:17:27.951843 5278                         Options.WAL_ttl_seconds: 0
2025/06/19-20:17:27.951846 5278                       Options.WAL_size_limit_MB: 0
2025/06/19-20:17:27.951849 5278                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-20:17:27.951853 5278             Options.manifest_preallocation_size: 4194304
2025/06/19-20:17:27.951857 5278                     Options.is_fd_close_on_exec: 1
2025/06/19-20:17:27.951860 5278                   Options.advise_random_on_open: 1
2025/06/19-20:17:27.951863 5278                    Options.db_write_buffer_size: 0
2025/06/19-20:17:27.951867 5278                    Options.write_buffer_manager: 000001EA6A04C770
2025/06/19-20:17:27.951871 5278         Options.access_hint_on_compaction_start: 1
2025/06/19-20:17:27.951875 5278           Options.random_access_max_buffer_size: 1048576
2025/06/19-20:17:27.951880 5278                      Options.use_adaptive_mutex: 0
2025/06/19-20:17:27.951928 5278                            Options.rate_limiter: 0000000000000000
2025/06/19-20:17:27.951938 5278     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-20:17:27.951942 5278                       Options.wal_recovery_mode: 2
2025/06/19-20:17:27.951946 5278                  Options.enable_thread_tracking: 0
2025/06/19-20:17:27.951951 5278                  Options.enable_pipelined_write: 0
2025/06/19-20:17:27.951954 5278                  Options.unordered_write: 0
2025/06/19-20:17:27.951958 5278         Options.allow_concurrent_memtable_write: 1
2025/06/19-20:17:27.951962 5278      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-20:17:27.951966 5278             Options.write_thread_max_yield_usec: 100
2025/06/19-20:17:27.951969 5278            Options.write_thread_slow_yield_usec: 3
2025/06/19-20:17:27.951972 5278                               Options.row_cache: None
2025/06/19-20:17:27.951974 5278                              Options.wal_filter: None
2025/06/19-20:17:27.951978 5278             Options.avoid_flush_during_recovery: 0
2025/06/19-20:17:27.951982 5278             Options.allow_ingest_behind: 0
2025/06/19-20:17:27.951986 5278             Options.two_write_queues: 0
2025/06/19-20:17:27.951990 5278             Options.manual_wal_flush: 0
2025/06/19-20:17:27.951992 5278             Options.wal_compression: 0
2025/06/19-20:17:27.951995 5278             Options.atomic_flush: 0
2025/06/19-20:17:27.951997 5278             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-20:17:27.952000 5278                 Options.persist_stats_to_disk: 0
2025/06/19-20:17:27.952003 5278                 Options.write_dbid_to_manifest: 0
2025/06/19-20:17:27.952006 5278                 Options.log_readahead_size: 0
2025/06/19-20:17:27.952008 5278                 Options.file_checksum_gen_factory: Unknown
2025/06/19-20:17:27.952011 5278                 Options.best_efforts_recovery: 0
2025/06/19-20:17:27.952014 5278                Options.max_bgerror_resume_count: 2147483647
2025/06/19-20:17:27.952017 5278            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-20:17:27.952019 5278             Options.allow_data_in_errors: 0
2025/06/19-20:17:27.952022 5278             Options.db_host_id: __hostname__
2025/06/19-20:17:27.952025 5278             Options.enforce_single_del_contracts: true
2025/06/19-20:17:27.952028 5278             Options.max_background_jobs: 2
2025/06/19-20:17:27.952030 5278             Options.max_background_compactions: 4
2025/06/19-20:17:27.952033 5278             Options.max_subcompactions: 1
2025/06/19-20:17:27.952053 5278             Options.avoid_flush_during_shutdown: 0
2025/06/19-20:17:27.952064 5278           Options.writable_file_max_buffer_size: 1048576
2025/06/19-20:17:27.952068 5278             Options.delayed_write_rate : 16777216
2025/06/19-20:17:27.952071 5278             Options.max_total_wal_size: 1073741824
2025/06/19-20:17:27.952075 5278             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-20:17:27.952079 5278                   Options.stats_dump_period_sec: 600
2025/06/19-20:17:27.952082 5278                 Options.stats_persist_period_sec: 600
2025/06/19-20:17:27.952087 5278                 Options.stats_history_buffer_size: 1048576
2025/06/19-20:17:27.952090 5278                          Options.max_open_files: -1
2025/06/19-20:17:27.952092 5278                          Options.bytes_per_sync: 0
2025/06/19-20:17:27.952095 5278                      Options.wal_bytes_per_sync: 0
2025/06/19-20:17:27.952098 5278                   Options.strict_bytes_per_sync: 0
2025/06/19-20:17:27.952100 5278       Options.compaction_readahead_size: 0
2025/06/19-20:17:27.952103 5278                  Options.max_background_flushes: 1
2025/06/19-20:17:27.952106 5278 Compression algorithms supported:
2025/06/19-20:17:27.952109 5278 	kZSTD supported: 1
2025/06/19-20:17:27.952112 5278 	kSnappyCompression supported: 1
2025/06/19-20:17:27.952115 5278 	kBZip2Compression supported: 0
2025/06/19-20:17:27.952146 5278 	kZlibCompression supported: 1
2025/06/19-20:17:27.952151 5278 	kLZ4Compression supported: 1
2025/06/19-20:17:27.952154 5278 	kXpressCompression supported: 0
2025/06/19-20:17:27.952156 5278 	kLZ4HCCompression supported: 1
2025/06/19-20:17:27.952159 5278 	kZSTDNotFinalCompression supported: 1
2025/06/19-20:17:27.952164 5278 Fast CRC32 supported: Not supported on x86
2025/06/19-20:17:27.952169 5278 DMutex implementation: std::mutex
2025/06/19-20:17:27.953301 5278 [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/MANIFEST-000019
2025/06/19-20:17:27.953477 5278 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-20:17:27.953483 5278               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:17:27.953486 5278           Options.merge_operator: StringAppendOperator
2025/06/19-20:17:27.953489 5278        Options.compaction_filter: None
2025/06/19-20:17:27.953492 5278        Options.compaction_filter_factory: None
2025/06/19-20:17:27.953494 5278  Options.sst_partitioner_factory: None
2025/06/19-20:17:27.953497 5278         Options.memtable_factory: SkipListFactory
2025/06/19-20:17:27.953500 5278            Options.table_factory: BlockBasedTable
2025/06/19-20:17:27.953530 5278            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001EA6A077AD0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000001EA69D7A8D0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:17:27.953533 5278        Options.write_buffer_size: 67108864
2025/06/19-20:17:27.953536 5278  Options.max_write_buffer_number: 3
2025/06/19-20:17:27.953539 5278          Options.compression: Snappy
2025/06/19-20:17:27.953542 5278                  Options.bottommost_compression: Disabled
2025/06/19-20:17:27.953545 5278       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:17:27.953548 5278   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:17:27.953550 5278             Options.num_levels: 7
2025/06/19-20:17:27.953553 5278        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:17:27.953555 5278     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:17:27.953558 5278     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:17:27.953561 5278            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:17:27.953563 5278                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:17:27.953566 5278               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:17:27.953569 5278         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.953572 5278         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.953575 5278         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:17:27.953577 5278                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:17:27.953583 5278         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.953587 5278         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.953590 5278            Options.compression_opts.window_bits: -14
2025/06/19-20:17:27.953592 5278                  Options.compression_opts.level: 32767
2025/06/19-20:17:27.953595 5278               Options.compression_opts.strategy: 0
2025/06/19-20:17:27.953598 5278         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.953600 5278         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.953603 5278         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.953606 5278         Options.compression_opts.parallel_threads: 1
2025/06/19-20:17:27.953608 5278                  Options.compression_opts.enabled: false
2025/06/19-20:17:27.953611 5278         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.953614 5278      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:17:27.953616 5278          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:17:27.953619 5278              Options.level0_stop_writes_trigger: 40
2025/06/19-20:17:27.953622 5278                   Options.target_file_size_base: 67108864
2025/06/19-20:17:27.953624 5278             Options.target_file_size_multiplier: 1
2025/06/19-20:17:27.953627 5278                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:17:27.953630 5278 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:17:27.953632 5278          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:17:27.953636 5278 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:17:27.953639 5278 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:17:27.953641 5278 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:17:27.953644 5278 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:17:27.953647 5278 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:17:27.953649 5278 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:17:27.953652 5278 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:17:27.953655 5278       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:17:27.953657 5278                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:17:27.953660 5278                        Options.arena_block_size: 1048576
2025/06/19-20:17:27.953663 5278   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:17:27.953665 5278   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:17:27.953668 5278                Options.disable_auto_compactions: 0
2025/06/19-20:17:27.953672 5278                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:17:27.953675 5278                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:17:27.953678 5278 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:17:27.953680 5278 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:17:27.953683 5278 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:17:27.953686 5278 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:17:27.953689 5278 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:17:27.953692 5278 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:17:27.953695 5278 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:17:27.953697 5278 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:17:27.953702 5278                   Options.table_properties_collectors: 
2025/06/19-20:17:27.953704 5278                   Options.inplace_update_support: 0
2025/06/19-20:17:27.953707 5278                 Options.inplace_update_num_locks: 10000
2025/06/19-20:17:27.953710 5278               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:17:27.953748 5278               Options.memtable_whole_key_filtering: 0
2025/06/19-20:17:27.953753 5278   Options.memtable_huge_page_size: 0
2025/06/19-20:17:27.953755 5278                           Options.bloom_locality: 0
2025/06/19-20:17:27.953758 5278                    Options.max_successive_merges: 0
2025/06/19-20:17:27.953761 5278                Options.optimize_filters_for_hits: 0
2025/06/19-20:17:27.953764 5278                Options.paranoid_file_checks: 0
2025/06/19-20:17:27.953766 5278                Options.force_consistency_checks: 1
2025/06/19-20:17:27.953769 5278                Options.report_bg_io_stats: 0
2025/06/19-20:17:27.953771 5278                               Options.ttl: 2592000
2025/06/19-20:17:27.953774 5278          Options.periodic_compaction_seconds: 0
2025/06/19-20:17:27.953777 5278  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:17:27.953780 5278                       Options.enable_blob_files: false
2025/06/19-20:17:27.953783 5278                           Options.min_blob_size: 0
2025/06/19-20:17:27.953785 5278                          Options.blob_file_size: 268435456
2025/06/19-20:17:27.953788 5278                   Options.blob_compression_type: NoCompression
2025/06/19-20:17:27.953791 5278          Options.enable_blob_garbage_collection: false
2025/06/19-20:17:27.953797 5278      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:17:27.953802 5278 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:17:27.953806 5278          Options.blob_compaction_readahead_size: 0
2025/06/19-20:17:27.953809 5278                Options.blob_file_starting_level: 0
2025/06/19-20:17:27.953812 5278 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:17:27.955331 5278 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-20:17:27.955342 5278               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:17:27.955345 5278           Options.merge_operator: StringAppendOperator
2025/06/19-20:17:27.955348 5278        Options.compaction_filter: None
2025/06/19-20:17:27.955350 5278        Options.compaction_filter_factory: None
2025/06/19-20:17:27.955353 5278  Options.sst_partitioner_factory: None
2025/06/19-20:17:27.955356 5278         Options.memtable_factory: SkipListFactory
2025/06/19-20:17:27.955359 5278            Options.table_factory: BlockBasedTable
2025/06/19-20:17:27.955383 5278            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001EA6A077AD0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 000001EA69D7A8D0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:17:27.955386 5278        Options.write_buffer_size: 67108864
2025/06/19-20:17:27.955389 5278  Options.max_write_buffer_number: 3
2025/06/19-20:17:27.955392 5278          Options.compression: Snappy
2025/06/19-20:17:27.955394 5278                  Options.bottommost_compression: Disabled
2025/06/19-20:17:27.955397 5278       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:17:27.955404 5278   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:17:27.955407 5278             Options.num_levels: 7
2025/06/19-20:17:27.955410 5278        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:17:27.955413 5278     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:17:27.955415 5278     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:17:27.955418 5278            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:17:27.955421 5278                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:17:27.955424 5278               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:17:27.955426 5278         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.955429 5278         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.955432 5278         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:17:27.955435 5278                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:17:27.955437 5278         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.955440 5278         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.955443 5278            Options.compression_opts.window_bits: -14
2025/06/19-20:17:27.955445 5278                  Options.compression_opts.level: 32767
2025/06/19-20:17:27.955448 5278               Options.compression_opts.strategy: 0
2025/06/19-20:17:27.955451 5278         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:17:27.955454 5278         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:17:27.955456 5278         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:17:27.955459 5278         Options.compression_opts.parallel_threads: 1
2025/06/19-20:17:27.955462 5278                  Options.compression_opts.enabled: false
2025/06/19-20:17:27.955464 5278         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:17:27.955467 5278      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:17:27.955470 5278          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:17:27.955472 5278              Options.level0_stop_writes_trigger: 40
2025/06/19-20:17:27.955475 5278                   Options.target_file_size_base: 67108864
2025/06/19-20:17:27.955478 5278             Options.target_file_size_multiplier: 1
2025/06/19-20:17:27.955481 5278                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:17:27.955483 5278 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:17:27.955486 5278          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:17:27.955489 5278 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:17:27.955492 5278 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:17:27.955495 5278 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:17:27.955498 5278 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:17:27.955500 5278 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:17:27.955504 5278 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:17:27.955508 5278 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:17:27.955511 5278       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:17:27.955515 5278                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:17:27.955518 5278                        Options.arena_block_size: 1048576
2025/06/19-20:17:27.955522 5278   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:17:27.955525 5278   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:17:27.955529 5278                Options.disable_auto_compactions: 0
2025/06/19-20:17:27.955534 5278                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:17:27.955539 5278                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:17:27.955550 5278 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:17:27.955554 5278 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:17:27.955557 5278 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:17:27.955559 5278 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:17:27.955562 5278 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:17:27.955566 5278 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:17:27.955569 5278 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:17:27.955571 5278 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:17:27.955578 5278                   Options.table_properties_collectors: 
2025/06/19-20:17:27.955581 5278                   Options.inplace_update_support: 0
2025/06/19-20:17:27.955583 5278                 Options.inplace_update_num_locks: 10000
2025/06/19-20:17:27.955586 5278               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:17:27.955589 5278               Options.memtable_whole_key_filtering: 0
2025/06/19-20:17:27.955592 5278   Options.memtable_huge_page_size: 0
2025/06/19-20:17:27.955595 5278                           Options.bloom_locality: 0
2025/06/19-20:17:27.955597 5278                    Options.max_successive_merges: 0
2025/06/19-20:17:27.955600 5278                Options.optimize_filters_for_hits: 0
2025/06/19-20:17:27.955603 5278                Options.paranoid_file_checks: 0
2025/06/19-20:17:27.955605 5278                Options.force_consistency_checks: 1
2025/06/19-20:17:27.955608 5278                Options.report_bg_io_stats: 0
2025/06/19-20:17:27.955611 5278                               Options.ttl: 2592000
2025/06/19-20:17:27.955613 5278          Options.periodic_compaction_seconds: 0
2025/06/19-20:17:27.955616 5278  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:17:27.955619 5278                       Options.enable_blob_files: false
2025/06/19-20:17:27.955621 5278                           Options.min_blob_size: 0
2025/06/19-20:17:27.955624 5278                          Options.blob_file_size: 268435456
2025/06/19-20:17:27.955627 5278                   Options.blob_compression_type: NoCompression
2025/06/19-20:17:27.955630 5278          Options.enable_blob_garbage_collection: false
2025/06/19-20:17:27.955632 5278      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:17:27.955635 5278 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:17:27.955639 5278          Options.blob_compaction_readahead_size: 0
2025/06/19-20:17:27.955641 5278                Options.blob_file_starting_level: 0
2025/06/19-20:17:27.955644 5278 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:17:27.959541 5278 [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/MANIFEST-000019 succeeded,manifest_file_number is 19, next_file_number is 21, last_sequence is 5, log_number is 13,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 13
2025/06/19-20:17:27.959556 5278 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 13
2025/06/19-20:17:27.959560 5278 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 13
2025/06/19-20:17:27.959905 5278 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9f-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-20:17:27.961792 5278 EVENT_LOG_v1 {"time_micros": 1750335447961783, "job": 1, "event": "recovery_started", "wal_files": [18]}
2025/06/19-20:17:27.961805 5278 [db\db_impl\db_impl_open.cc:1029] Recovering log #18 mode 2
2025/06/19-20:17:27.963838 5278 EVENT_LOG_v1 {"time_micros": 1750335447963793, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 22, "file_size": 1184, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 6, "largest_seqno": 8, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335447, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9f-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "EI1DCCUQWNAYVIOM60Q8", "orig_file_number": 22, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:17:27.967708 5278 EVENT_LOG_v1 {"time_micros": 1750335447967674, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 23, "file_size": 1190, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 7, "largest_seqno": 9, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335447, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9f-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "EI1DCCUQWNAYVIOM60Q8", "orig_file_number": 23, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:17:27.970822 5278 EVENT_LOG_v1 {"time_micros": 1750335447970814, "job": 1, "event": "recovery_finished"}
2025/06/19-20:17:27.971338 5278 [db\version_set.cc:5051] Creating manifest 25
2025/06/19-20:17:27.978724 5278 [file\delete_scheduler.cc:77] Deleted file D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/000018.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-20:17:27.978749 5278 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 000001EA6A4E7CB0
2025/06/19-20:17:27.979382 5278 DB pointer 000001EA6A49B040
2025/06/19-20:17:33.989219 1ce0 [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-20:17:33.989242 1ce0 [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 6.0 total, 6.0 interval
Cumulative writes: 3 writes, 4 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 4 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.34 KB   0.3      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      3/0    3.34 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6.0 total, 6.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000001EA69D7A8D0#8752 capacity: 512.00 MB usage: 2.85 KB table_size: 4096 occupancy: 15 collections: 1 last_copies: 1 last_secs: 0.000183 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(6,0.82 KB,0.000155531%) IndexBlock(6,0.55 KB,0.000104681%) OtherBlock(2,0.21 KB,3.98606e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      3/0    3.41 KB   0.3      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      3/0    3.41 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6.0 total, 6.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@000001EA69D7A8D0#8752 capacity: 512.00 MB usage: 2.85 KB table_size: 4096 occupancy: 15 collections: 1 last_copies: 1 last_secs: 0.000183 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(6,0.82 KB,0.000155531%) IndexBlock(6,0.55 KB,0.000104681%) OtherBlock(2,0.21 KB,3.98606e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 9 Average: 15.3333  StdDev: 10.17
Min: 6  Median: 12.5000  Max: 37
Percentiles: P50: 12.50 P75: 31.00 P99: 37.00 P99.9: 37.00 P99.99: 37.00
------------------------------------------------------
(       4,       6 ]        1  11.111%  11.111% ##
(       6,      10 ]        2  22.222%  33.333% ####
(      10,      15 ]        3  33.333%  66.667% #######
(      22,      34 ]        1  11.111%  77.778% ##
(      34,      51 ]        1  11.111%  88.889% ##


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 9 Average: 13.6667  StdDev: 7.60
Min: 6  Median: 11.8750  Max: 28
Percentiles: P50: 11.88 P75: 14.69 P99: 28.00 P99.9: 28.00 P99.99: 28.00
------------------------------------------------------
(       4,       6 ]        2  22.222%  22.222% ####
(       6,      10 ]        1  11.111%  33.333% ##
(      10,      15 ]        4  44.444%  77.778% #########
(      22,      34 ]        2  22.222% 100.000% ####

2025/06/19-20:17:33.990057 1ce0 [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 14
rocksdb.block.cache.hit COUNT : 8
rocksdb.block.cache.add COUNT : 14
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 6
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 6
rocksdb.block.cache.index.bytes.insert COUNT : 562
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 8
rocksdb.block.cache.data.hit COUNT : 8
rocksdb.block.cache.data.add COUNT : 8
rocksdb.block.cache.data.bytes.insert COUNT : 1049
rocksdb.block.cache.bytes.read COUNT : 1064
rocksdb.block.cache.bytes.write COUNT : 1611
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 5
rocksdb.l0.hit COUNT : 5
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 4
rocksdb.number.keys.read COUNT : 5
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 166
rocksdb.bytes.read COUNT : 170
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 4
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 2
rocksdb.number.db.next.found COUNT : 3
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 194
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 6
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 166
rocksdb.write.self COUNT : 3
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 3
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2374
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 3
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 14257
rocksdb.non.last.level.read.count COUNT : 18
rocksdb.block.checksum.compute.count COUNT : 26
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 8.000000 P95 : 46.000000 P99 : 46.000000 P100 : 46.000000 COUNT : 5 SUM : 102
rocksdb.db.write.micros P50 : 140.000000 P95 : 464.000000 P99 : 464.000000 P100 : 464.000000 COUNT : 3 SUM : 620
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 555.000000 P95 : 565.000000 P99 : 565.000000 P100 : 565.000000 COUNT : 2 SUM : 1120
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 407.000000 P95 : 407.000000 P99 : 407.000000 P100 : 407.000000 COUNT : 1 SUM : 407
rocksdb.manifest.file.sync.micros P50 : 345.000000 P95 : 345.000000 P99 : 345.000000 P100 : 345.000000 COUNT : 1 SUM : 345
rocksdb.table.open.io.micros P50 : 130.000000 P95 : 198.000000 P99 : 198.000000 P100 : 198.000000 COUNT : 6 SUM : 808
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.833333 P95 : 17.000000 P99 : 17.000000 P100 : 17.000000 COUNT : 20 SUM : 76
rocksdb.write.raw.block.micros P50 : 0.750000 P95 : 3.700000 P99 : 3.940000 P100 : 4.000000 COUNT : 12 SUM : 17
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 11.875000 P95 : 35.700000 P99 : 37.000000 P100 : 37.000000 COUNT : 18 SUM : 261
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 34.000000 P95 : 34.000000 P99 : 34.000000 P100 : 34.000000 COUNT : 5 SUM : 170
rocksdb.bytes.per.write P50 : 31.000000 P95 : 103.000000 P99 : 103.000000 P100 : 103.000000 COUNT : 3 SUM : 166
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1085.000000 P95 : 1099.000000 P99 : 1099.000000 P100 : 1099.000000 COUNT : 6 SUM : 6319
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-20:17:34.981373 1ce0 [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-20:17:34.981410 1ce0 [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-20:17:34.981413 1ce0 [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0

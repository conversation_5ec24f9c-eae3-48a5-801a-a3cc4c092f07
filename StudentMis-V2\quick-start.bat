@echo off
echo Starting Tsinghua StudentMIS V2.0 Complete System...
echo.

mvn clean install -DskipTests -q

start "Gateway-8080" cmd /k "cd studentmis-gateway && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

start "Auth-8081" cmd /k "cd studentmis-auth && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

start "Student-8082" cmd /k "cd studentmis-student && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

start "Grade-8083" cmd /k "cd studentmis-grade && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

start "Analytics-8084" cmd /k "cd studentmis-analytics && mvn spring-boot:run"

echo.
echo System started! Services:
echo - Gateway: http://localhost:8080
echo - Auth: http://localhost:8081  
echo - Student: http://localhost:8082
echo - Grade: http://localhost:8083
echo - Analytics: http://localhost:8084
echo.
echo Test your data: curl http://localhost:8082/api/students/simple/by-student-id/2024140520
echo.

pause

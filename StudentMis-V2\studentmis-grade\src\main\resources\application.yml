server:
  port: 8083

spring:
  application:
    name: studentmis-grade
  profiles:
    active: dev
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: root
    password: xhxabc

# MyBatis配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: edu.tsinghua.studentmis.grade.entity

# 日志配置
logging:
  level:
    edu.tsinghua.studentmis: debug

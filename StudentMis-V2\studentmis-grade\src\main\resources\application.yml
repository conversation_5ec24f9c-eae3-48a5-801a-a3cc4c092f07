server:
  port: 8083

spring:
  application:
    name: studentmis-grade
  profiles:
    active: dev
  config:
    import: "optional:nacos:studentmis-grade.yml"
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        group: DEFAULT_GROUP
        file-extension: yml
        import-check:
          enabled: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************
    username: root
    password: 123456

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    edu.tsinghua.studentmis: debug

package edu.tsinghua.studentmis.student.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 学生基本信息实体（简化版）
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "学生基本信息")
public class StudentSimple {
    
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "学号")
    @NotBlank(message = "学号不能为空")
    @Pattern(regexp = "^[0-9]{8,12}$", message = "学号格式不正确")
    private String studentId;

    @Schema(description = "姓名")
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String name;

    @Schema(description = "英文姓名")
    @Size(max = 100, message = "英文姓名长度不能超过100个字符")
    private String nameEn;

    @Schema(description = "性别")
    @NotNull(message = "性别不能为空")
    private Gender gender;

    @Schema(description = "出生日期")
    @NotNull(message = "出生日期不能为空")
    @Past(message = "出生日期必须是过去的日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthDate;

    @Schema(description = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "入学日期")
    @NotNull(message = "入学日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate admissionDate;

    @Schema(description = "学籍状态")
    @NotNull(message = "学籍状态不能为空")
    private StudentStatus status;

    @Schema(description = "专业ID")
    @NotNull(message = "专业ID不能为空")
    private Long majorId;

    @Schema(description = "班级ID")
    @NotNull(message = "班级ID不能为空")
    private Long classId;

    /**
     * 性别枚举
     */
    public enum Gender {
        MALE("男"),
        FEMALE("女"),
        OTHER("其他");

        private final String description;

        Gender(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 学籍状态枚举
     */
    public enum StudentStatus {
        ACTIVE("在读"),
        SUSPENDED("休学"),
        GRADUATED("已毕业"),
        DROPPED("退学"),
        TRANSFERRED("转学");

        private final String description;

        StudentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查学生是否在读
     */
    public boolean isActive() {
        return StudentStatus.ACTIVE.equals(this.status);
    }

    /**
     * 检查学生是否已毕业
     */
    public boolean isGraduated() {
        return StudentStatus.GRADUATED.equals(this.status);
    }

    /**
     * 计算年龄
     */
    public int getAge() {
        if (birthDate == null) {
            return 0;
        }
        return LocalDate.now().getYear() - birthDate.getYear();
    }
}

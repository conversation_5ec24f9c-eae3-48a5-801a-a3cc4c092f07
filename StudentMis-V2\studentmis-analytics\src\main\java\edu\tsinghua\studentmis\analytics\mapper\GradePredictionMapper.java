package edu.tsinghua.studentmis.analytics.mapper;

import edu.tsinghua.studentmis.analytics.entity.GradePrediction;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 成绩预测Mapper接口
 *
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface GradePredictionMapper {

    /**
     * 插入预测记录
     */
    @Insert("INSERT INTO grade_prediction (student_id, schedule_id, predicted_score, confidence, created_at) " +
            "VALUES (#{studentId}, #{scheduleId}, #{predictedScore}, #{confidence}, NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(GradePrediction gradePrediction);

    /**
     * 根据ID查询预测记录
     */
    @Select("SELECT * FROM grade_prediction WHERE id = #{id}")
    GradePrediction selectById(@Param("id") Long id);

    /**
     * 更新预测记录
     */
    @Update("UPDATE grade_prediction SET predicted_score = #{predictedScore}, confidence = #{confidence} WHERE id = #{id}")
    int updateById(GradePrediction gradePrediction);

    /**
     * 删除预测记录
     */
    @Delete("DELETE FROM grade_prediction WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 获取历史成绩数据
     */
    @Select("SELECT student_id, usual_score, midterm_score, final_score, total_score " +
            "FROM grade_record WHERE schedule_id = #{scheduleId} AND status = 'PUBLISHED'")
    List<Map<String, Object>> getHistoricalGradeData(@Param("scheduleId") Long scheduleId);

    /**
     * 获取当前平时成绩
     */
    @Select("SELECT usual_score FROM grade_record " +
            "WHERE student_id = #{studentId} AND schedule_id = #{scheduleId}")
    Double getCurrentUsualScore(@Param("studentId") Long studentId, 
                               @Param("scheduleId") Long scheduleId);

    /**
     * 获取当前各项成绩
     */
    @Select("SELECT usual_score, midterm_score, final_score, total_score " +
            "FROM grade_record WHERE student_id = #{studentId} AND schedule_id = #{scheduleId}")
    Map<String, Object> getCurrentScores(@Param("studentId") Long studentId, 
                                        @Param("scheduleId") Long scheduleId);
}

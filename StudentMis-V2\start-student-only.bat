@echo off
chcp 65001 >nul
echo ==========================================
echo   清华大学学生成绩管理系统 V2.0
echo   学生管理服务 - 单独启动版本
echo ==========================================

echo.
echo 正在启动学生管理服务...

cd studentmis-student

echo [编译] 正在编译学生服务...
mvn clean compile -q
if %errorlevel% neq 0 (
    echo [错误] 编译失败
    pause
    exit /b 1
)

echo [启动] 学生管理服务启动中...
echo.

mvn spring-boot:run

echo.
echo ==========================================
echo   学生管理服务已启动
echo ==========================================
echo.
echo 服务地址: http://localhost:8082
echo.
echo 测试您的数据:
echo   curl http://localhost:8082/api/students/simple/by-student-id/2024140520
echo.
echo 查看所有学生:
echo   curl http://localhost:8082/api/students/simple
echo.

pause

@echo off
chcp 65001 >nul
echo ==========================================
echo   清华大学学生成绩管理系统 V2.0
echo   StudentMIS V2 - 完整系统启动脚本
echo ==========================================

echo.
echo 正在检查环境...

:: 检查Java版本
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Java环境未正确配置
    pause
    exit /b 1
)

:: 检查Maven
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Maven环境未正确配置
    pause
    exit /b 1
)

:: 检查MySQL服务
netstat -an | findstr :3306 >nul
if %errorlevel% neq 0 (
    echo [警告] MySQL服务可能未启动，请确保MySQL正在运行
)

echo [信息] 环境检查完成
echo.

echo 正在清理并编译项目...
mvn clean install -DskipTests -q
if %errorlevel% neq 0 (
    echo [错误] 项目编译失败
    pause
    exit /b 1
)

echo [成功] 项目编译完成
echo.

echo 正在启动所有微服务...
echo.

:: 启动网关服务
echo [启动] API网关服务 (端口8080)
start "StudentMIS Gateway" cmd /k "title StudentMIS Gateway && cd studentmis-gateway && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

:: 启动认证服务
echo [启动] 认证服务 (端口8081)
start "StudentMIS Auth" cmd /k "title StudentMIS Auth && cd studentmis-auth && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

:: 启动学生服务
echo [启动] 学生管理服务 (端口8082)
start "StudentMIS Student" cmd /k "title StudentMIS Student && cd studentmis-student && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

:: 启动成绩服务
echo [启动] 成绩管理服务 (端口8083)
start "StudentMIS Grade" cmd /k "title StudentMIS Grade && cd studentmis-grade && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

:: 启动分析服务
echo [启动] 数据分析服务 (端口8084)
start "StudentMIS Analytics" cmd /k "title StudentMIS Analytics && cd studentmis-analytics && mvn spring-boot:run"

echo.
echo ==========================================
echo   所有服务启动命令已执行
echo   请等待各服务完全启动（约2-3分钟）
echo ==========================================
echo.
echo 服务端口分配：
echo   - API网关:     http://localhost:8080
echo   - 认证服务:     http://localhost:8081
echo   - 学生服务:     http://localhost:8082
echo   - 成绩服务:     http://localhost:8083
echo   - 分析服务:     http://localhost:8084
echo.
echo 测试命令：
echo   curl http://localhost:8082/api/students/simple/test
echo   curl http://localhost:8082/api/students/simple/by-student-id/2024140520
echo.
echo API文档：
echo   http://localhost:8080/swagger-ui.html
echo.

pause

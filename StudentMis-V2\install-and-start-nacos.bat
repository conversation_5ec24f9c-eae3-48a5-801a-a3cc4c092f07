@echo off
chcp 65001 >nul
echo ==========================================
echo   Nacos 注册中心安装启动脚本
echo   StudentMIS V2 - Nacos Setup
echo ==========================================

set NACOS_DIR=D:\nacos
set NACOS_VERSION=2.2.3

echo.
echo 正在检查Nacos安装...

:: 检查是否已安装
if exist "%NACOS_DIR%\nacos\bin\startup.cmd" (
    echo [信息] 检测到已安装的Nacos
    goto :start_nacos
)

echo [信息] 未检测到Nacos，开始下载安装...

:: 创建目录
if not exist "%NACOS_DIR%" mkdir "%NACOS_DIR%"
cd /d "%NACOS_DIR%"

echo [下载] 正在下载Nacos %NACOS_VERSION%...
echo 下载地址: https://github.com/alibaba/nacos/releases/download/%NACOS_VERSION%/nacos-server-%NACOS_VERSION%.zip

:: 尝试下载
curl -L -o nacos-server-%NACOS_VERSION%.zip https://github.com/alibaba/nacos/releases/download/%NACOS_VERSION%/nacos-server-%NACOS_VERSION%.zip

:: 检查下载是否成功
if not exist "nacos-server-%NACOS_VERSION%.zip" (
    echo [错误] 下载失败，尝试使用镜像地址...
    curl -L -o nacos-server-%NACOS_VERSION%.zip https://mirror.ghproxy.com/https://github.com/alibaba/nacos/releases/download/%NACOS_VERSION%/nacos-server-%NACOS_VERSION%.zip
)

if not exist "nacos-server-%NACOS_VERSION%.zip" (
    echo [错误] 下载失败，请手动下载Nacos
    echo 下载地址: https://github.com/alibaba/nacos/releases/download/%NACOS_VERSION%/nacos-server-%NACOS_VERSION%.zip
    echo 下载后放置到: %NACOS_DIR%
    pause
    exit /b 1
)

echo [解压] 正在解压Nacos...
tar -xf nacos-server-%NACOS_VERSION%.zip
if %errorlevel% neq 0 (
    echo [错误] 解压失败，请检查文件完整性
    pause
    exit /b 1
)

echo [成功] Nacos安装完成

:start_nacos
echo.
echo [启动] 正在启动Nacos服务器...
cd /d "%NACOS_DIR%\nacos\bin"

echo 启动命令: startup.cmd -m standalone
echo 请等待Nacos完全启动...
echo.

:: 启动Nacos
startup.cmd -m standalone

echo.
echo ==========================================
echo   Nacos启动完成
echo ==========================================
echo.
echo Nacos控制台地址: http://localhost:8848/nacos
echo 默认用户名: nacos
echo 默认密码: nacos
echo.
echo 请等待30秒确保Nacos完全启动，然后可以启动StudentMIS服务
echo.

pause

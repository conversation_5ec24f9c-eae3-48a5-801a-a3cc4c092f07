USE studentmis_v2;

CREATE TABLE IF NOT EXISTS stu_basic_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(50) NOT NULL,
    name_en VARCHAR(100),
    gender ENUM('MALE', 'FEMALE', 'OTHER') NOT NULL,
    birth_date DATE,
    phone VARCHAR(20),
    email VARCHAR(100),
    admission_date DATE,
    status ENUM('ACTIVE', 'SUSPENDED', 'GRADUATED', 'DROPPED', 'TRANSFERRED') DEFAULT 'ACTIVE',
    major_id BIGINT,
    class_id BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT IGNORE INTO stu_basic_info (student_id, name, name_en, gender, birth_date, phone, email, admission_date, status, major_id, class_id) VALUES
('2024140520', 'XuHaoxiang', '<PERSON>oxiang', 'MALE', '2002-05-20', '18626425051', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140518', 'WangZiqi', 'Wang Ziqi', 'MALE', '2002-05-18', '13800140518', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140525', 'ZhangQingxiang', 'Zhang Qingxiang', 'MALE', '2002-05-25', '13800140525', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140485', 'CaiJiaxuan', 'Cai Jiaxuan', 'FEMALE', '2002-04-25', '13800140485', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140499', 'LiuChuandong', 'Liu Chuandong', 'MALE', '2002-04-29', '13800140499', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140508', 'ShenLei', 'Shen Lei', 'MALE', '2002-05-08', '13800140508', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241),
('2024140486', 'ChengXuefeng', 'Cheng Xuefeng', 'MALE', '2002-04-26', '13800140486', '<EMAIL>', '2024-09-01', 'ACTIVE', 1, 4241);

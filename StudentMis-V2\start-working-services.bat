@echo off
chcp 65001 >nul
echo ==========================================
echo   清华大学学生成绩管理系统 V2.0
echo   StudentMIS V2 - 核心服务启动脚本
echo ==========================================

echo.
echo 正在检查环境...

:: 检查Java版本
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Java环境未正确配置
    pause
    exit /b 1
)

:: 检查Maven
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Maven环境未正确配置
    pause
    exit /b 1
)

:: 检查MySQL服务
netstat -an | findstr :3306 >nul
if %errorlevel% neq 0 (
    echo [警告] MySQL服务可能未启动，请确保MySQL正在运行
)

echo [信息] 环境检查完成
echo.

echo 正在清理并编译学生服务...
cd studentmis-student
mvn clean compile -q
if %errorlevel% neq 0 (
    echo [错误] 学生服务编译失败
    pause
    exit /b 1
)

echo [成功] 学生服务编译完成
echo.

echo 正在启动学生管理服务...
echo [启动] 学生管理服务 (端口8082)
echo.

:: 启动学生服务
mvn spring-boot:run

echo.
echo ==========================================
echo   学生管理服务启动完成
echo ==========================================
echo.
echo 服务地址：
echo   - 学生服务:     http://localhost:8082
echo.
echo 测试命令：
echo   curl http://localhost:8082/api/students/simple/test
echo   curl http://localhost:8082/api/students/simple/by-student-id/2024140520
echo   curl http://localhost:8082/api/students/simple
echo.
echo API文档：
echo   http://localhost:8082/swagger-ui.html
echo.

pause

2025/06/19-20:27:06.698122 34b0 RocksDB version: 7.7.3
2025/06/19-20:27:06.698200 34b0 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-20:27:06.698212 34b0 Compile date 2022-10-24 17:17:55
2025/06/19-20:27:06.698229 34b0 DB SUMMARY
2025/06/19-20:27:06.698237 34b0 DB Session ID:  Z9GJHPZR9V5DOT5YCAS2
2025/06/19-20:27:06.698980 34b0 CURRENT file:  CURRENT
2025/06/19-20:27:06.698993 34b0 IDENTITY file:  IDENTITY
2025/06/19-20:27:06.699078 34b0 MANIFEST file:  MANIFEST-000025 size: 686 Bytes
2025/06/19-20:27:06.699094 34b0 SST files in d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log dir, Total Num: 6, files: 000010.sst 000011.sst 000016.sst 000017.sst 000022.sst 000023.sst 
2025/06/19-20:27:06.699217 34b0 Write Ahead Log file in d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log: 000024.log size: 187 ; 
2025/06/19-20:27:06.699228 34b0                         Options.error_if_exists: 0
2025/06/19-20:27:06.699230 34b0                       Options.create_if_missing: 1
2025/06/19-20:27:06.699233 34b0                         Options.paranoid_checks: 1
2025/06/19-20:27:06.699235 34b0             Options.flush_verify_memtable_count: 1
2025/06/19-20:27:06.699237 34b0                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-20:27:06.699239 34b0        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-20:27:06.699241 34b0                                     Options.env: 0000018DC40F71D0
2025/06/19-20:27:06.699244 34b0                                      Options.fs: WinFS
2025/06/19-20:27:06.699246 34b0                                Options.info_log: 0000018DC4CDC6D0
2025/06/19-20:27:06.699248 34b0                Options.max_file_opening_threads: 16
2025/06/19-20:27:06.699251 34b0                              Options.statistics: 0000018DFEB06140
2025/06/19-20:27:06.699253 34b0                               Options.use_fsync: 0
2025/06/19-20:27:06.699255 34b0                       Options.max_log_file_size: 0
2025/06/19-20:27:06.699257 34b0                  Options.max_manifest_file_size: 1073741824
2025/06/19-20:27:06.699260 34b0                   Options.log_file_time_to_roll: 0
2025/06/19-20:27:06.699262 34b0                       Options.keep_log_file_num: 100
2025/06/19-20:27:06.699264 34b0                    Options.recycle_log_file_num: 0
2025/06/19-20:27:06.699266 34b0                         Options.allow_fallocate: 1
2025/06/19-20:27:06.699268 34b0                        Options.allow_mmap_reads: 0
2025/06/19-20:27:06.699270 34b0                       Options.allow_mmap_writes: 0
2025/06/19-20:27:06.699272 34b0                        Options.use_direct_reads: 0
2025/06/19-20:27:06.699274 34b0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-20:27:06.699276 34b0          Options.create_missing_column_families: 1
2025/06/19-20:27:06.699279 34b0                              Options.db_log_dir: 
2025/06/19-20:27:06.699281 34b0                                 Options.wal_dir: 
2025/06/19-20:27:06.699283 34b0                Options.table_cache_numshardbits: 6
2025/06/19-20:27:06.699285 34b0                         Options.WAL_ttl_seconds: 0
2025/06/19-20:27:06.699287 34b0                       Options.WAL_size_limit_MB: 0
2025/06/19-20:27:06.699289 34b0                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-20:27:06.699291 34b0             Options.manifest_preallocation_size: 4194304
2025/06/19-20:27:06.699293 34b0                     Options.is_fd_close_on_exec: 1
2025/06/19-20:27:06.699295 34b0                   Options.advise_random_on_open: 1
2025/06/19-20:27:06.699298 34b0                    Options.db_write_buffer_size: 0
2025/06/19-20:27:06.699300 34b0                    Options.write_buffer_manager: 0000018DC40F8FD0
2025/06/19-20:27:06.699302 34b0         Options.access_hint_on_compaction_start: 1
2025/06/19-20:27:06.699304 34b0           Options.random_access_max_buffer_size: 1048576
2025/06/19-20:27:06.699323 34b0                      Options.use_adaptive_mutex: 0
2025/06/19-20:27:06.699327 34b0                            Options.rate_limiter: 0000000000000000
2025/06/19-20:27:06.699329 34b0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-20:27:06.699332 34b0                       Options.wal_recovery_mode: 2
2025/06/19-20:27:06.699334 34b0                  Options.enable_thread_tracking: 0
2025/06/19-20:27:06.699336 34b0                  Options.enable_pipelined_write: 0
2025/06/19-20:27:06.699338 34b0                  Options.unordered_write: 0
2025/06/19-20:27:06.699340 34b0         Options.allow_concurrent_memtable_write: 1
2025/06/19-20:27:06.699342 34b0      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-20:27:06.699344 34b0             Options.write_thread_max_yield_usec: 100
2025/06/19-20:27:06.699346 34b0            Options.write_thread_slow_yield_usec: 3
2025/06/19-20:27:06.699348 34b0                               Options.row_cache: None
2025/06/19-20:27:06.699350 34b0                              Options.wal_filter: None
2025/06/19-20:27:06.699353 34b0             Options.avoid_flush_during_recovery: 0
2025/06/19-20:27:06.699355 34b0             Options.allow_ingest_behind: 0
2025/06/19-20:27:06.699357 34b0             Options.two_write_queues: 0
2025/06/19-20:27:06.699359 34b0             Options.manual_wal_flush: 0
2025/06/19-20:27:06.699361 34b0             Options.wal_compression: 0
2025/06/19-20:27:06.699363 34b0             Options.atomic_flush: 0
2025/06/19-20:27:06.699365 34b0             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-20:27:06.699367 34b0                 Options.persist_stats_to_disk: 0
2025/06/19-20:27:06.699369 34b0                 Options.write_dbid_to_manifest: 0
2025/06/19-20:27:06.699371 34b0                 Options.log_readahead_size: 0
2025/06/19-20:27:06.699373 34b0                 Options.file_checksum_gen_factory: Unknown
2025/06/19-20:27:06.699376 34b0                 Options.best_efforts_recovery: 0
2025/06/19-20:27:06.699378 34b0                Options.max_bgerror_resume_count: 2147483647
2025/06/19-20:27:06.699380 34b0            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-20:27:06.699382 34b0             Options.allow_data_in_errors: 0
2025/06/19-20:27:06.699385 34b0             Options.db_host_id: __hostname__
2025/06/19-20:27:06.699387 34b0             Options.enforce_single_del_contracts: true
2025/06/19-20:27:06.699389 34b0             Options.max_background_jobs: 2
2025/06/19-20:27:06.699391 34b0             Options.max_background_compactions: 4
2025/06/19-20:27:06.699393 34b0             Options.max_subcompactions: 1
2025/06/19-20:27:06.699395 34b0             Options.avoid_flush_during_shutdown: 0
2025/06/19-20:27:06.699397 34b0           Options.writable_file_max_buffer_size: 1048576
2025/06/19-20:27:06.699399 34b0             Options.delayed_write_rate : 16777216
2025/06/19-20:27:06.699402 34b0             Options.max_total_wal_size: 1073741824
2025/06/19-20:27:06.699404 34b0             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-20:27:06.699406 34b0                   Options.stats_dump_period_sec: 600
2025/06/19-20:27:06.699408 34b0                 Options.stats_persist_period_sec: 600
2025/06/19-20:27:06.699410 34b0                 Options.stats_history_buffer_size: 1048576
2025/06/19-20:27:06.699412 34b0                          Options.max_open_files: -1
2025/06/19-20:27:06.699414 34b0                          Options.bytes_per_sync: 0
2025/06/19-20:27:06.699417 34b0                      Options.wal_bytes_per_sync: 0
2025/06/19-20:27:06.699419 34b0                   Options.strict_bytes_per_sync: 0
2025/06/19-20:27:06.699421 34b0       Options.compaction_readahead_size: 0
2025/06/19-20:27:06.699423 34b0                  Options.max_background_flushes: 1
2025/06/19-20:27:06.699425 34b0 Compression algorithms supported:
2025/06/19-20:27:06.699433 34b0 	kZSTD supported: 1
2025/06/19-20:27:06.699436 34b0 	kSnappyCompression supported: 1
2025/06/19-20:27:06.699438 34b0 	kBZip2Compression supported: 0
2025/06/19-20:27:06.699451 34b0 	kZlibCompression supported: 1
2025/06/19-20:27:06.699454 34b0 	kLZ4Compression supported: 1
2025/06/19-20:27:06.699456 34b0 	kXpressCompression supported: 0
2025/06/19-20:27:06.699458 34b0 	kLZ4HCCompression supported: 1
2025/06/19-20:27:06.699460 34b0 	kZSTDNotFinalCompression supported: 1
2025/06/19-20:27:06.699467 34b0 Fast CRC32 supported: Not supported on x86
2025/06/19-20:27:06.699470 34b0 DMutex implementation: std::mutex
2025/06/19-20:27:06.700386 34b0 [db\version_set.cc:5531] Recovering from manifest file: d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000025
2025/06/19-20:27:06.700665 34b0 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-20:27:06.700675 34b0               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:27:06.700678 34b0           Options.merge_operator: StringAppendOperator
2025/06/19-20:27:06.700680 34b0        Options.compaction_filter: None
2025/06/19-20:27:06.700682 34b0        Options.compaction_filter_factory: None
2025/06/19-20:27:06.700684 34b0  Options.sst_partitioner_factory: None
2025/06/19-20:27:06.700687 34b0         Options.memtable_factory: SkipListFactory
2025/06/19-20:27:06.700689 34b0            Options.table_factory: BlockBasedTable
2025/06/19-20:27:06.700716 34b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018DC35B8D20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018DFEB1B2A0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:27:06.700719 34b0        Options.write_buffer_size: 67108864
2025/06/19-20:27:06.700721 34b0  Options.max_write_buffer_number: 3
2025/06/19-20:27:06.700724 34b0          Options.compression: Snappy
2025/06/19-20:27:06.700726 34b0                  Options.bottommost_compression: Disabled
2025/06/19-20:27:06.700728 34b0       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:27:06.700730 34b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:27:06.700732 34b0             Options.num_levels: 7
2025/06/19-20:27:06.700734 34b0        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:27:06.700736 34b0     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:27:06.700738 34b0     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:27:06.700740 34b0            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:27:06.700743 34b0                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:27:06.700745 34b0               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:27:06.700747 34b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.700749 34b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.700751 34b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:27:06.700753 34b0                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:27:06.700757 34b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.700760 34b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.700762 34b0            Options.compression_opts.window_bits: -14
2025/06/19-20:27:06.700764 34b0                  Options.compression_opts.level: 32767
2025/06/19-20:27:06.700766 34b0               Options.compression_opts.strategy: 0
2025/06/19-20:27:06.700768 34b0         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.700770 34b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.700772 34b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.700774 34b0         Options.compression_opts.parallel_threads: 1
2025/06/19-20:27:06.700777 34b0                  Options.compression_opts.enabled: false
2025/06/19-20:27:06.700779 34b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.700781 34b0      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:27:06.700783 34b0          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:27:06.700785 34b0              Options.level0_stop_writes_trigger: 40
2025/06/19-20:27:06.700787 34b0                   Options.target_file_size_base: 67108864
2025/06/19-20:27:06.700789 34b0             Options.target_file_size_multiplier: 1
2025/06/19-20:27:06.700791 34b0                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:27:06.700793 34b0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:27:06.700795 34b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:27:06.700802 34b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:27:06.700804 34b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:27:06.700806 34b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:27:06.700808 34b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:27:06.700810 34b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:27:06.700812 34b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:27:06.700814 34b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:27:06.700816 34b0       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:27:06.700818 34b0                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:27:06.700820 34b0                        Options.arena_block_size: 1048576
2025/06/19-20:27:06.700822 34b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:27:06.700824 34b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:27:06.700827 34b0                Options.disable_auto_compactions: 0
2025/06/19-20:27:06.700829 34b0                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:27:06.700832 34b0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:27:06.700834 34b0 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:27:06.700836 34b0 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:27:06.700838 34b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:27:06.700840 34b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:27:06.700842 34b0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:27:06.700845 34b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:27:06.700847 34b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:27:06.700849 34b0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:27:06.700853 34b0                   Options.table_properties_collectors: 
2025/06/19-20:27:06.700855 34b0                   Options.inplace_update_support: 0
2025/06/19-20:27:06.700857 34b0                 Options.inplace_update_num_locks: 10000
2025/06/19-20:27:06.700859 34b0               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:27:06.700888 34b0               Options.memtable_whole_key_filtering: 0
2025/06/19-20:27:06.700891 34b0   Options.memtable_huge_page_size: 0
2025/06/19-20:27:06.700893 34b0                           Options.bloom_locality: 0
2025/06/19-20:27:06.700895 34b0                    Options.max_successive_merges: 0
2025/06/19-20:27:06.700897 34b0                Options.optimize_filters_for_hits: 0
2025/06/19-20:27:06.700899 34b0                Options.paranoid_file_checks: 0
2025/06/19-20:27:06.700901 34b0                Options.force_consistency_checks: 1
2025/06/19-20:27:06.700903 34b0                Options.report_bg_io_stats: 0
2025/06/19-20:27:06.700905 34b0                               Options.ttl: 2592000
2025/06/19-20:27:06.700907 34b0          Options.periodic_compaction_seconds: 0
2025/06/19-20:27:06.700909 34b0  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:27:06.700911 34b0                       Options.enable_blob_files: false
2025/06/19-20:27:06.700913 34b0                           Options.min_blob_size: 0
2025/06/19-20:27:06.700915 34b0                          Options.blob_file_size: 268435456
2025/06/19-20:27:06.700918 34b0                   Options.blob_compression_type: NoCompression
2025/06/19-20:27:06.700920 34b0          Options.enable_blob_garbage_collection: false
2025/06/19-20:27:06.700922 34b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:27:06.700924 34b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:27:06.700927 34b0          Options.blob_compaction_readahead_size: 0
2025/06/19-20:27:06.700929 34b0                Options.blob_file_starting_level: 0
2025/06/19-20:27:06.700931 34b0 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:27:06.702391 34b0 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-20:27:06.702401 34b0               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:27:06.702403 34b0           Options.merge_operator: StringAppendOperator
2025/06/19-20:27:06.702405 34b0        Options.compaction_filter: None
2025/06/19-20:27:06.702407 34b0        Options.compaction_filter_factory: None
2025/06/19-20:27:06.702410 34b0  Options.sst_partitioner_factory: None
2025/06/19-20:27:06.702412 34b0         Options.memtable_factory: SkipListFactory
2025/06/19-20:27:06.702414 34b0            Options.table_factory: BlockBasedTable
2025/06/19-20:27:06.702438 34b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018DC35B8D20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018DFEB1B2A0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:27:06.702441 34b0        Options.write_buffer_size: 67108864
2025/06/19-20:27:06.702443 34b0  Options.max_write_buffer_number: 3
2025/06/19-20:27:06.702445 34b0          Options.compression: Snappy
2025/06/19-20:27:06.702447 34b0                  Options.bottommost_compression: Disabled
2025/06/19-20:27:06.702481 34b0       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:27:06.702485 34b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:27:06.702488 34b0             Options.num_levels: 7
2025/06/19-20:27:06.702490 34b0        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:27:06.702493 34b0     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:27:06.702496 34b0     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:27:06.702498 34b0            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:27:06.702501 34b0                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:27:06.702504 34b0               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:27:06.702506 34b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.702509 34b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.702511 34b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:27:06.702513 34b0                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:27:06.702515 34b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.702517 34b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.702519 34b0            Options.compression_opts.window_bits: -14
2025/06/19-20:27:06.702521 34b0                  Options.compression_opts.level: 32767
2025/06/19-20:27:06.702523 34b0               Options.compression_opts.strategy: 0
2025/06/19-20:27:06.702525 34b0         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.702528 34b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.702530 34b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.702532 34b0         Options.compression_opts.parallel_threads: 1
2025/06/19-20:27:06.702534 34b0                  Options.compression_opts.enabled: false
2025/06/19-20:27:06.702536 34b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.702538 34b0      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:27:06.702540 34b0          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:27:06.702542 34b0              Options.level0_stop_writes_trigger: 40
2025/06/19-20:27:06.702544 34b0                   Options.target_file_size_base: 67108864
2025/06/19-20:27:06.702546 34b0             Options.target_file_size_multiplier: 1
2025/06/19-20:27:06.702548 34b0                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:27:06.702550 34b0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:27:06.702552 34b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:27:06.702555 34b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:27:06.702557 34b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:27:06.702559 34b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:27:06.702561 34b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:27:06.702563 34b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:27:06.702565 34b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:27:06.702567 34b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:27:06.702570 34b0       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:27:06.702572 34b0                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:27:06.702574 34b0                        Options.arena_block_size: 1048576
2025/06/19-20:27:06.702576 34b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:27:06.702578 34b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:27:06.702580 34b0                Options.disable_auto_compactions: 0
2025/06/19-20:27:06.702583 34b0                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:27:06.702586 34b0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:27:06.702604 34b0 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:27:06.702606 34b0 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:27:06.702609 34b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:27:06.702611 34b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:27:06.702613 34b0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:27:06.702615 34b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:27:06.702618 34b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:27:06.702620 34b0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:27:06.702624 34b0                   Options.table_properties_collectors: 
2025/06/19-20:27:06.702626 34b0                   Options.inplace_update_support: 0
2025/06/19-20:27:06.702628 34b0                 Options.inplace_update_num_locks: 10000
2025/06/19-20:27:06.702630 34b0               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:27:06.702633 34b0               Options.memtable_whole_key_filtering: 0
2025/06/19-20:27:06.702635 34b0   Options.memtable_huge_page_size: 0
2025/06/19-20:27:06.702637 34b0                           Options.bloom_locality: 0
2025/06/19-20:27:06.702639 34b0                    Options.max_successive_merges: 0
2025/06/19-20:27:06.702641 34b0                Options.optimize_filters_for_hits: 0
2025/06/19-20:27:06.702643 34b0                Options.paranoid_file_checks: 0
2025/06/19-20:27:06.702645 34b0                Options.force_consistency_checks: 1
2025/06/19-20:27:06.702647 34b0                Options.report_bg_io_stats: 0
2025/06/19-20:27:06.702649 34b0                               Options.ttl: 2592000
2025/06/19-20:27:06.702651 34b0          Options.periodic_compaction_seconds: 0
2025/06/19-20:27:06.702653 34b0  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:27:06.702655 34b0                       Options.enable_blob_files: false
2025/06/19-20:27:06.702657 34b0                           Options.min_blob_size: 0
2025/06/19-20:27:06.702659 34b0                          Options.blob_file_size: 268435456
2025/06/19-20:27:06.702662 34b0                   Options.blob_compression_type: NoCompression
2025/06/19-20:27:06.702664 34b0          Options.enable_blob_garbage_collection: false
2025/06/19-20:27:06.702666 34b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:27:06.702668 34b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:27:06.702671 34b0          Options.blob_compaction_readahead_size: 0
2025/06/19-20:27:06.702673 34b0                Options.blob_file_starting_level: 0
2025/06/19-20:27:06.702675 34b0 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:27:06.705737 34b0 [db\version_set.cc:5579] Recovered from manifest file:d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000025 succeeded,manifest_file_number is 25, next_file_number is 27, last_sequence is 9, log_number is 19,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 19
2025/06/19-20:27:06.705747 34b0 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 19
2025/06/19-20:27:06.705750 34b0 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 19
2025/06/19-20:27:06.706038 34b0 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9d-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-20:27:06.707811 34b0 EVENT_LOG_v1 {"time_micros": 1750336026707795, "job": 1, "event": "recovery_started", "wal_files": [24]}
2025/06/19-20:27:06.707822 34b0 [db\db_impl\db_impl_open.cc:1029] Recovering log #24 mode 2
2025/06/19-20:27:06.709484 34b0 EVENT_LOG_v1 {"time_micros": 1750336026709451, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 28, "file_size": 1184, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 10, "largest_seqno": 12, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750336026, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9d-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "Z9GJHPZR9V5DOT5YCAS2", "orig_file_number": 28, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:27:06.713591 34b0 EVENT_LOG_v1 {"time_micros": 1750336026713562, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 29, "file_size": 1190, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 11, "largest_seqno": 13, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750336026, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9d-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "Z9GJHPZR9V5DOT5YCAS2", "orig_file_number": 29, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:27:06.715910 34b0 EVENT_LOG_v1 {"time_micros": 1750336026715905, "job": 1, "event": "recovery_finished"}
2025/06/19-20:27:06.716281 34b0 [db\version_set.cc:5051] Creating manifest 31
2025/06/19-20:27:06.722993 34b0 [file\delete_scheduler.cc:77] Deleted file d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/000024.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-20:27:06.723023 34b0 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 0000018DC35BD960
2025/06/19-20:27:06.723695 34b0 DB pointer 0000018DC5DD6040
2025/06/19-20:27:06.724211 29cc [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-20:27:06.724223 29cc [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/0    4.49 KB   0.4      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      4/0    4.49 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.8      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0000018DFEB1B2A0#16792 capacity: 512.00 MB usage: 2.25 KB table_size: 4096 occupancy: 13 collections: 1 last_copies: 1 last_secs: 0.000121 secs_since: 0
Block cache entry stats(count,size,portion): IndexBlock(8,0.73 KB,0.000138953%) OtherBlock(4,0.42 KB,7.97212e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      4/0    4.57 KB   0.4      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      4/0    4.57 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0000018DFEB1B2A0#16792 capacity: 512.00 MB usage: 2.25 KB table_size: 4096 occupancy: 13 collections: 1 last_copies: 1 last_secs: 0.000121 secs_since: 0
Block cache entry stats(count,size,portion): IndexBlock(8,0.73 KB,0.000138953%) OtherBlock(4,0.42 KB,7.97212e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 7 Average: 15.0000  StdDev: 7.75
Min: 6  Median: 12.5000  Max: 24
Percentiles: P50: 12.50 P75: 16.75 P99: 24.00 P99.9: 24.00 P99.99: 24.00
------------------------------------------------------
(       4,       6 ]        1  14.286%  14.286% ###
(       6,      10 ]        1  14.286%  28.571% ###
(      10,      15 ]        3  42.857%  71.429% #########
(      15,      22 ]        1  14.286%  85.714% ###
(      22,      34 ]        2  28.571% 114.286% ######


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 8 Average: 12.1250  StdDev: 4.54
Min: 7  Median: 10.0000  Max: 18
Percentiles: P50: 10.00 P75: 17.33 P99: 18.00 P99.9: 18.00 P99.99: 18.00
------------------------------------------------------
(       6,      10 ]        4  50.000%  50.000% ##########
(      10,      15 ]        1  12.500%  62.500% ###
(      15,      22 ]        3  37.500% 100.000% ########

2025/06/19-20:27:06.724863 29cc [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 12
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 12
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 8
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 8
rocksdb.block.cache.index.bytes.insert COUNT : 746
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 4
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 4
rocksdb.block.cache.data.bytes.insert COUNT : 428
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 1174
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 0
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 0
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 0
rocksdb.number.db.next COUNT : 0
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 0
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 0
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 8
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 0
rocksdb.wal.bytes COUNT : 0
rocksdb.write.self COUNT : 0
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 0
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2374
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 0
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 0
rocksdb.num.iterator.deleted COUNT : 0
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 18572
rocksdb.non.last.level.read.count COUNT : 16
rocksdb.block.checksum.compute.count COUNT : 28
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 438.000000 P95 : 438.000000 P99 : 438.000000 P100 : 438.000000 COUNT : 2 SUM : 835
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.manifest.file.sync.micros P50 : 367.000000 P95 : 367.000000 P99 : 367.000000 P100 : 367.000000 COUNT : 1 SUM : 367
rocksdb.table.open.io.micros P50 : 98.666667 P95 : 553.333333 P99 : 568.000000 P100 : 568.000000 COUNT : 8 SUM : 2004
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.666667 P95 : 3.666667 P99 : 3.933333 P100 : 4.000000 COUNT : 20 SUM : 24
rocksdb.write.raw.block.micros P50 : 0.600000 P95 : 11.000000 P99 : 11.000000 P100 : 11.000000 COUNT : 12 SUM : 18
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 12.500000 P95 : 29.000000 P99 : 29.000000 P100 : 29.000000 COUNT : 16 SUM : 224
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1085.000000 P95 : 1099.000000 P99 : 1099.000000 P100 : 1099.000000 COUNT : 8 SUM : 8511
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-20:27:07.772839 29cc [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-20:27:07.772864 29cc [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-20:27:07.772870 29cc [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0

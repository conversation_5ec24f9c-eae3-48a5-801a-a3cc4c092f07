2025/06/19-20:27:06.783803 34b0 RocksDB version: 7.7.3
2025/06/19-20:27:06.783881 34b0 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-20:27:06.783895 34b0 Compile date 2022-10-24 17:17:55
2025/06/19-20:27:06.783905 34b0 DB SUMMARY
2025/06/19-20:27:06.783993 34b0 DB Session ID:  Z9GJHPZR9V5DOT5YCAS3
2025/06/19-20:27:06.784683 34b0 CURRENT file:  CURRENT
2025/06/19-20:27:06.784699 34b0 IDENTITY file:  IDENTITY
2025/06/19-20:27:06.784794 34b0 MANIFEST file:  MANIFEST-000025 size: 686 Bytes
2025/06/19-20:27:06.784828 34b0 SST files in d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log dir, Total Num: 6, files: 000010.sst 000011.sst 000016.sst 000017.sst 000022.sst 000023.sst 
2025/06/19-20:27:06.784964 34b0 Write Ahead Log file in d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log: 000024.log size: 187 ; 
2025/06/19-20:27:06.784970 34b0                         Options.error_if_exists: 0
2025/06/19-20:27:06.784973 34b0                       Options.create_if_missing: 1
2025/06/19-20:27:06.784975 34b0                         Options.paranoid_checks: 1
2025/06/19-20:27:06.784977 34b0             Options.flush_verify_memtable_count: 1
2025/06/19-20:27:06.784980 34b0                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-20:27:06.784982 34b0        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-20:27:06.784984 34b0                                     Options.env: 0000018DC40F71D0
2025/06/19-20:27:06.784987 34b0                                      Options.fs: WinFS
2025/06/19-20:27:06.784989 34b0                                Options.info_log: 0000018DC4CE1F90
2025/06/19-20:27:06.784991 34b0                Options.max_file_opening_threads: 16
2025/06/19-20:27:06.784994 34b0                              Options.statistics: 0000018DFD13B340
2025/06/19-20:27:06.784996 34b0                               Options.use_fsync: 0
2025/06/19-20:27:06.784998 34b0                       Options.max_log_file_size: 0
2025/06/19-20:27:06.785000 34b0                  Options.max_manifest_file_size: 1073741824
2025/06/19-20:27:06.785003 34b0                   Options.log_file_time_to_roll: 0
2025/06/19-20:27:06.785005 34b0                       Options.keep_log_file_num: 100
2025/06/19-20:27:06.785007 34b0                    Options.recycle_log_file_num: 0
2025/06/19-20:27:06.785009 34b0                         Options.allow_fallocate: 1
2025/06/19-20:27:06.785011 34b0                        Options.allow_mmap_reads: 0
2025/06/19-20:27:06.785013 34b0                       Options.allow_mmap_writes: 0
2025/06/19-20:27:06.785015 34b0                        Options.use_direct_reads: 0
2025/06/19-20:27:06.785017 34b0                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-20:27:06.785020 34b0          Options.create_missing_column_families: 1
2025/06/19-20:27:06.785022 34b0                              Options.db_log_dir: 
2025/06/19-20:27:06.785024 34b0                                 Options.wal_dir: 
2025/06/19-20:27:06.785027 34b0                Options.table_cache_numshardbits: 6
2025/06/19-20:27:06.785029 34b0                         Options.WAL_ttl_seconds: 0
2025/06/19-20:27:06.785031 34b0                       Options.WAL_size_limit_MB: 0
2025/06/19-20:27:06.785033 34b0                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-20:27:06.785035 34b0             Options.manifest_preallocation_size: 4194304
2025/06/19-20:27:06.785038 34b0                     Options.is_fd_close_on_exec: 1
2025/06/19-20:27:06.785040 34b0                   Options.advise_random_on_open: 1
2025/06/19-20:27:06.785042 34b0                    Options.db_write_buffer_size: 0
2025/06/19-20:27:06.785044 34b0                    Options.write_buffer_manager: 0000018DC40F5F10
2025/06/19-20:27:06.785047 34b0         Options.access_hint_on_compaction_start: 1
2025/06/19-20:27:06.785049 34b0           Options.random_access_max_buffer_size: 1048576
2025/06/19-20:27:06.785071 34b0                      Options.use_adaptive_mutex: 0
2025/06/19-20:27:06.785075 34b0                            Options.rate_limiter: 0000000000000000
2025/06/19-20:27:06.785078 34b0     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-20:27:06.785081 34b0                       Options.wal_recovery_mode: 2
2025/06/19-20:27:06.785083 34b0                  Options.enable_thread_tracking: 0
2025/06/19-20:27:06.785086 34b0                  Options.enable_pipelined_write: 0
2025/06/19-20:27:06.785095 34b0                  Options.unordered_write: 0
2025/06/19-20:27:06.785121 34b0         Options.allow_concurrent_memtable_write: 1
2025/06/19-20:27:06.785125 34b0      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-20:27:06.785127 34b0             Options.write_thread_max_yield_usec: 100
2025/06/19-20:27:06.785129 34b0            Options.write_thread_slow_yield_usec: 3
2025/06/19-20:27:06.785132 34b0                               Options.row_cache: None
2025/06/19-20:27:06.785134 34b0                              Options.wal_filter: None
2025/06/19-20:27:06.785136 34b0             Options.avoid_flush_during_recovery: 0
2025/06/19-20:27:06.785138 34b0             Options.allow_ingest_behind: 0
2025/06/19-20:27:06.785141 34b0             Options.two_write_queues: 0
2025/06/19-20:27:06.785143 34b0             Options.manual_wal_flush: 0
2025/06/19-20:27:06.785145 34b0             Options.wal_compression: 0
2025/06/19-20:27:06.785147 34b0             Options.atomic_flush: 0
2025/06/19-20:27:06.785149 34b0             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-20:27:06.785151 34b0                 Options.persist_stats_to_disk: 0
2025/06/19-20:27:06.785154 34b0                 Options.write_dbid_to_manifest: 0
2025/06/19-20:27:06.785156 34b0                 Options.log_readahead_size: 0
2025/06/19-20:27:06.785158 34b0                 Options.file_checksum_gen_factory: Unknown
2025/06/19-20:27:06.785160 34b0                 Options.best_efforts_recovery: 0
2025/06/19-20:27:06.785162 34b0                Options.max_bgerror_resume_count: 2147483647
2025/06/19-20:27:06.785165 34b0            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-20:27:06.785167 34b0             Options.allow_data_in_errors: 0
2025/06/19-20:27:06.785169 34b0             Options.db_host_id: __hostname__
2025/06/19-20:27:06.785171 34b0             Options.enforce_single_del_contracts: true
2025/06/19-20:27:06.785173 34b0             Options.max_background_jobs: 2
2025/06/19-20:27:06.785176 34b0             Options.max_background_compactions: 4
2025/06/19-20:27:06.785178 34b0             Options.max_subcompactions: 1
2025/06/19-20:27:06.785180 34b0             Options.avoid_flush_during_shutdown: 0
2025/06/19-20:27:06.785182 34b0           Options.writable_file_max_buffer_size: 1048576
2025/06/19-20:27:06.785185 34b0             Options.delayed_write_rate : 16777216
2025/06/19-20:27:06.785187 34b0             Options.max_total_wal_size: 1073741824
2025/06/19-20:27:06.785189 34b0             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-20:27:06.785191 34b0                   Options.stats_dump_period_sec: 600
2025/06/19-20:27:06.785194 34b0                 Options.stats_persist_period_sec: 600
2025/06/19-20:27:06.785196 34b0                 Options.stats_history_buffer_size: 1048576
2025/06/19-20:27:06.785198 34b0                          Options.max_open_files: -1
2025/06/19-20:27:06.785201 34b0                          Options.bytes_per_sync: 0
2025/06/19-20:27:06.785203 34b0                      Options.wal_bytes_per_sync: 0
2025/06/19-20:27:06.785205 34b0                   Options.strict_bytes_per_sync: 0
2025/06/19-20:27:06.785207 34b0       Options.compaction_readahead_size: 0
2025/06/19-20:27:06.785210 34b0                  Options.max_background_flushes: 1
2025/06/19-20:27:06.785212 34b0 Compression algorithms supported:
2025/06/19-20:27:06.785215 34b0 	kZSTD supported: 1
2025/06/19-20:27:06.785218 34b0 	kSnappyCompression supported: 1
2025/06/19-20:27:06.785220 34b0 	kBZip2Compression supported: 0
2025/06/19-20:27:06.785234 34b0 	kZlibCompression supported: 1
2025/06/19-20:27:06.785237 34b0 	kLZ4Compression supported: 1
2025/06/19-20:27:06.785240 34b0 	kXpressCompression supported: 0
2025/06/19-20:27:06.785242 34b0 	kLZ4HCCompression supported: 1
2025/06/19-20:27:06.785244 34b0 	kZSTDNotFinalCompression supported: 1
2025/06/19-20:27:06.785248 34b0 Fast CRC32 supported: Not supported on x86
2025/06/19-20:27:06.785250 34b0 DMutex implementation: std::mutex
2025/06/19-20:27:06.786175 34b0 [db\version_set.cc:5531] Recovering from manifest file: d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000025
2025/06/19-20:27:06.786377 34b0 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-20:27:06.786387 34b0               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:27:06.786390 34b0           Options.merge_operator: StringAppendOperator
2025/06/19-20:27:06.786393 34b0        Options.compaction_filter: None
2025/06/19-20:27:06.786395 34b0        Options.compaction_filter_factory: None
2025/06/19-20:27:06.786397 34b0  Options.sst_partitioner_factory: None
2025/06/19-20:27:06.786399 34b0         Options.memtable_factory: SkipListFactory
2025/06/19-20:27:06.786401 34b0            Options.table_factory: BlockBasedTable
2025/06/19-20:27:06.786425 34b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018DC4FE3990)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018DFEB18660
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:27:06.786427 34b0        Options.write_buffer_size: 67108864
2025/06/19-20:27:06.786430 34b0  Options.max_write_buffer_number: 3
2025/06/19-20:27:06.786432 34b0          Options.compression: Snappy
2025/06/19-20:27:06.786434 34b0                  Options.bottommost_compression: Disabled
2025/06/19-20:27:06.786437 34b0       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:27:06.786439 34b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:27:06.786441 34b0             Options.num_levels: 7
2025/06/19-20:27:06.786443 34b0        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:27:06.786446 34b0     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:27:06.786449 34b0     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:27:06.786452 34b0            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:27:06.786454 34b0                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:27:06.786456 34b0               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:27:06.786458 34b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.786460 34b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.786463 34b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:27:06.786465 34b0                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:27:06.786475 34b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.786479 34b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.786482 34b0            Options.compression_opts.window_bits: -14
2025/06/19-20:27:06.786485 34b0                  Options.compression_opts.level: 32767
2025/06/19-20:27:06.786488 34b0               Options.compression_opts.strategy: 0
2025/06/19-20:27:06.786491 34b0         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.786493 34b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.786496 34b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.786499 34b0         Options.compression_opts.parallel_threads: 1
2025/06/19-20:27:06.786502 34b0                  Options.compression_opts.enabled: false
2025/06/19-20:27:06.786505 34b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.786508 34b0      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:27:06.786512 34b0          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:27:06.786515 34b0              Options.level0_stop_writes_trigger: 40
2025/06/19-20:27:06.786519 34b0                   Options.target_file_size_base: 67108864
2025/06/19-20:27:06.786522 34b0             Options.target_file_size_multiplier: 1
2025/06/19-20:27:06.786524 34b0                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:27:06.786528 34b0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:27:06.786531 34b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:27:06.786536 34b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:27:06.786539 34b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:27:06.786543 34b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:27:06.786546 34b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:27:06.786550 34b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:27:06.786553 34b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:27:06.786556 34b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:27:06.786559 34b0       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:27:06.786562 34b0                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:27:06.786566 34b0                        Options.arena_block_size: 1048576
2025/06/19-20:27:06.786569 34b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:27:06.786573 34b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:27:06.786576 34b0                Options.disable_auto_compactions: 0
2025/06/19-20:27:06.786581 34b0                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:27:06.786586 34b0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:27:06.786590 34b0 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:27:06.786593 34b0 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:27:06.786596 34b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:27:06.786600 34b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:27:06.786603 34b0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:27:06.786606 34b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:27:06.786609 34b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:27:06.786611 34b0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:27:06.786615 34b0                   Options.table_properties_collectors: 
2025/06/19-20:27:06.786618 34b0                   Options.inplace_update_support: 0
2025/06/19-20:27:06.786620 34b0                 Options.inplace_update_num_locks: 10000
2025/06/19-20:27:06.786623 34b0               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:27:06.786670 34b0               Options.memtable_whole_key_filtering: 0
2025/06/19-20:27:06.786675 34b0   Options.memtable_huge_page_size: 0
2025/06/19-20:27:06.786677 34b0                           Options.bloom_locality: 0
2025/06/19-20:27:06.786679 34b0                    Options.max_successive_merges: 0
2025/06/19-20:27:06.786682 34b0                Options.optimize_filters_for_hits: 0
2025/06/19-20:27:06.786684 34b0                Options.paranoid_file_checks: 0
2025/06/19-20:27:06.786687 34b0                Options.force_consistency_checks: 1
2025/06/19-20:27:06.786689 34b0                Options.report_bg_io_stats: 0
2025/06/19-20:27:06.786691 34b0                               Options.ttl: 2592000
2025/06/19-20:27:06.786693 34b0          Options.periodic_compaction_seconds: 0
2025/06/19-20:27:06.786696 34b0  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:27:06.786698 34b0                       Options.enable_blob_files: false
2025/06/19-20:27:06.786700 34b0                           Options.min_blob_size: 0
2025/06/19-20:27:06.786702 34b0                          Options.blob_file_size: 268435456
2025/06/19-20:27:06.786705 34b0                   Options.blob_compression_type: NoCompression
2025/06/19-20:27:06.786707 34b0          Options.enable_blob_garbage_collection: false
2025/06/19-20:27:06.786709 34b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:27:06.786712 34b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:27:06.786714 34b0          Options.blob_compaction_readahead_size: 0
2025/06/19-20:27:06.786717 34b0                Options.blob_file_starting_level: 0
2025/06/19-20:27:06.786719 34b0 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:27:06.788116 34b0 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-20:27:06.788127 34b0               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:27:06.788129 34b0           Options.merge_operator: StringAppendOperator
2025/06/19-20:27:06.788132 34b0        Options.compaction_filter: None
2025/06/19-20:27:06.788134 34b0        Options.compaction_filter_factory: None
2025/06/19-20:27:06.788136 34b0  Options.sst_partitioner_factory: None
2025/06/19-20:27:06.788138 34b0         Options.memtable_factory: SkipListFactory
2025/06/19-20:27:06.788140 34b0            Options.table_factory: BlockBasedTable
2025/06/19-20:27:06.788160 34b0            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000018DC4FE3990)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018DFEB18660
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:27:06.788163 34b0        Options.write_buffer_size: 67108864
2025/06/19-20:27:06.788165 34b0  Options.max_write_buffer_number: 3
2025/06/19-20:27:06.788167 34b0          Options.compression: Snappy
2025/06/19-20:27:06.788169 34b0                  Options.bottommost_compression: Disabled
2025/06/19-20:27:06.788172 34b0       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:27:06.788214 34b0   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:27:06.788217 34b0             Options.num_levels: 7
2025/06/19-20:27:06.788219 34b0        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:27:06.788221 34b0     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:27:06.788223 34b0     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:27:06.788225 34b0            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:27:06.788228 34b0                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:27:06.788230 34b0               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:27:06.788232 34b0         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.788234 34b0         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.788236 34b0         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:27:06.788238 34b0                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:27:06.788240 34b0         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.788243 34b0         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.788245 34b0            Options.compression_opts.window_bits: -14
2025/06/19-20:27:06.788247 34b0                  Options.compression_opts.level: 32767
2025/06/19-20:27:06.788249 34b0               Options.compression_opts.strategy: 0
2025/06/19-20:27:06.788251 34b0         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:27:06.788253 34b0         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:27:06.788255 34b0         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:27:06.788257 34b0         Options.compression_opts.parallel_threads: 1
2025/06/19-20:27:06.788259 34b0                  Options.compression_opts.enabled: false
2025/06/19-20:27:06.788262 34b0         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:27:06.788264 34b0      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:27:06.788266 34b0          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:27:06.788268 34b0              Options.level0_stop_writes_trigger: 40
2025/06/19-20:27:06.788270 34b0                   Options.target_file_size_base: 67108864
2025/06/19-20:27:06.788272 34b0             Options.target_file_size_multiplier: 1
2025/06/19-20:27:06.788274 34b0                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:27:06.788276 34b0 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:27:06.788278 34b0          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:27:06.788281 34b0 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:27:06.788283 34b0 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:27:06.788285 34b0 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:27:06.788287 34b0 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:27:06.788290 34b0 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:27:06.788292 34b0 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:27:06.788294 34b0 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:27:06.788296 34b0       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:27:06.788298 34b0                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:27:06.788300 34b0                        Options.arena_block_size: 1048576
2025/06/19-20:27:06.788302 34b0   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:27:06.788304 34b0   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:27:06.788306 34b0                Options.disable_auto_compactions: 0
2025/06/19-20:27:06.788309 34b0                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:27:06.788312 34b0                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:27:06.788329 34b0 Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:27:06.788332 34b0 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:27:06.788334 34b0 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:27:06.788337 34b0 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:27:06.788339 34b0 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:27:06.788342 34b0 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:27:06.788344 34b0 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:27:06.788346 34b0 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:27:06.788351 34b0                   Options.table_properties_collectors: 
2025/06/19-20:27:06.788353 34b0                   Options.inplace_update_support: 0
2025/06/19-20:27:06.788355 34b0                 Options.inplace_update_num_locks: 10000
2025/06/19-20:27:06.788357 34b0               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:27:06.788360 34b0               Options.memtable_whole_key_filtering: 0
2025/06/19-20:27:06.788362 34b0   Options.memtable_huge_page_size: 0
2025/06/19-20:27:06.788364 34b0                           Options.bloom_locality: 0
2025/06/19-20:27:06.788366 34b0                    Options.max_successive_merges: 0
2025/06/19-20:27:06.788368 34b0                Options.optimize_filters_for_hits: 0
2025/06/19-20:27:06.788370 34b0                Options.paranoid_file_checks: 0
2025/06/19-20:27:06.788372 34b0                Options.force_consistency_checks: 1
2025/06/19-20:27:06.788374 34b0                Options.report_bg_io_stats: 0
2025/06/19-20:27:06.788376 34b0                               Options.ttl: 2592000
2025/06/19-20:27:06.788379 34b0          Options.periodic_compaction_seconds: 0
2025/06/19-20:27:06.788381 34b0  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:27:06.788383 34b0                       Options.enable_blob_files: false
2025/06/19-20:27:06.788385 34b0                           Options.min_blob_size: 0
2025/06/19-20:27:06.788387 34b0                          Options.blob_file_size: 268435456
2025/06/19-20:27:06.788389 34b0                   Options.blob_compression_type: NoCompression
2025/06/19-20:27:06.788391 34b0          Options.enable_blob_garbage_collection: false
2025/06/19-20:27:06.788393 34b0      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:27:06.788395 34b0 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:27:06.788398 34b0          Options.blob_compaction_readahead_size: 0
2025/06/19-20:27:06.788400 34b0                Options.blob_file_starting_level: 0
2025/06/19-20:27:06.788402 34b0 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:27:06.792174 34b0 [db\version_set.cc:5579] Recovered from manifest file:d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000025 succeeded,manifest_file_number is 25, next_file_number is 27, last_sequence is 9, log_number is 19,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 19
2025/06/19-20:27:06.792190 34b0 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 19
2025/06/19-20:27:06.792193 34b0 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 19
2025/06/19-20:27:06.792573 34b0 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9e-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-20:27:06.794447 34b0 EVENT_LOG_v1 {"time_micros": 1750336026794440, "job": 1, "event": "recovery_started", "wal_files": [24]}
2025/06/19-20:27:06.794458 34b0 [db\db_impl\db_impl_open.cc:1029] Recovering log #24 mode 2
2025/06/19-20:27:06.795979 34b0 EVENT_LOG_v1 {"time_micros": 1750336026795949, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 28, "file_size": 1184, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 10, "largest_seqno": 12, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750336026, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9e-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "Z9GJHPZR9V5DOT5YCAS3", "orig_file_number": 28, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:27:06.799287 34b0 EVENT_LOG_v1 {"time_micros": 1750336026799257, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 29, "file_size": 1190, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 11, "largest_seqno": 13, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 1, "num_merge_operands": 0, "num_range_deletions": 1, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750336026, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9e-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "Z9GJHPZR9V5DOT5YCAS3", "orig_file_number": 29, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:27:06.802036 34b0 EVENT_LOG_v1 {"time_micros": 1750336026802029, "job": 1, "event": "recovery_finished"}
2025/06/19-20:27:06.803572 34b0 [db\version_set.cc:5051] Creating manifest 31
2025/06/19-20:27:06.810726 34b0 [file\delete_scheduler.cc:77] Deleted file d:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/000024.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-20:27:06.810749 34b0 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 0000018DC35BBCE0
2025/06/19-20:27:06.811381 34b0 DB pointer 0000018DF89D1980

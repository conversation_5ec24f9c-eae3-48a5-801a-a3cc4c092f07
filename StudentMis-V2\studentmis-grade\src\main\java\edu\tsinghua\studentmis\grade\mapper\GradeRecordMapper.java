package edu.tsinghua.studentmis.grade.mapper;

import edu.tsinghua.studentmis.grade.entity.GradeRecord;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 成绩记录Mapper接口
 *
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface GradeRecordMapper {

    /**
     * 插入成绩记录
     */
    @Insert("INSERT INTO grade_record (student_id, schedule_id, score, grade_type, created_at, updated_at) " +
            "VALUES (#{studentId}, #{scheduleId}, #{score}, #{gradeType}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(GradeRecord gradeRecord);

    /**
     * 根据ID查询成绩记录
     */
    @Select("SELECT * FROM grade_record WHERE id = #{id}")
    GradeRecord selectById(@Param("id") Long id);

    /**
     * 更新成绩记录
     */
    @Update("UPDATE grade_record SET score = #{score}, grade_type = #{gradeType}, updated_at = NOW() WHERE id = #{id}")
    int updateById(GradeRecord gradeRecord);

    /**
     * 删除成绩记录
     */
    @Delete("DELETE FROM grade_record WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 查询学生成绩
     */
    @Select("SELECT * FROM grade_record WHERE student_id = #{studentId} AND schedule_id = #{scheduleId}")
    List<GradeRecord> selectStudentGrades(@Param("studentId") Long studentId,
                                         @Param("scheduleId") Long scheduleId);
}
